<!-- Accordians Start -->

<div class="accordion" id="accordionExample">
  <div class="card accordian-card mb-16">
    <div class="card-header accordian-card-header" id="headingNin">
      <h2 class="mb-0">
        <button class="btn btn-link btn-block text-left accordionBtn d-flex justify-content-between align-items-center p-0"
          type="button" data-toggle="collapse" data-target="#collapseNin" aria-expanded="true"
          aria-controls="collapseNin">{{'theme_configurations' | labelName}}<span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 1024 1024" fill="#212529">
              <path
                d="M104.704 338.752a64 64 0 0 1 90.496 0l316.8 316.8l316.8-316.8a64 64 0 0 1 90.496 90.496L557.248 791.296a64 64 0 0 1-90.496 0L104.704 429.248a64 64 0 0 1 0-90.496" />
            </svg>
          </span>
        </button>
      </h2>
    </div>

    <div id="collapseNin" class="collapse" aria-labelledby="headingNin">
      <div class="row custom-row theme-detail-row">
        <div class="col-md-6">
          <div class="card mb-0">
            <div class="card-header">
              <h3 class="card-title">Theme Detail ({{ theme ? theme.name:'' }}) </h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body">

              <div class="ibox-content">

                <form #form="ngForm">

                  <div class="form-group row">
                    <label for="inputEmail3" class="col-sm-6 col-form-label">{{'theme_type' | labelName}}</label>
                    <div class="col-sm-6">
                      <div class="form-group clearfix">
                        <div class="icheck-primary d-inline">
                          <input type="radio" id="themeTypeLight1" (change)="changeThemeType('light')" value="light"
                            name="theme_type" [(ngModel)]="theme.palette.type"
                            [disabled]="theme.name !== disable_theme" />
                          <label for="themeTypeLight1">{{'light' | labelName}}</label>
                        </div>
                        &nbsp;
                        <div class="icheck-primary d-inline">
                          <input type="radio" id="themeTypeDark1" value="dark" (change)="changeThemeType('dark')"
                            [(ngModel)]="theme.palette.type" name="theme_type"
                            [disabled]="theme.name !== disable_theme" />
                          <label for="themeTypeDark1">{{'dark' | labelName}}</label>
                        </div>
                      </div>
                    </div>
                  </div>


                  <div class="form-group row" *ngIf="enableSportsMobileHome || this.permissionService.isSuperAdmin">
                    <label for="sportlayout" class="col-sm-6 col-form-label">{{'select_sports_landing_page_layout' | labelName}}<small>{{'for_mobile' | labelName}}</small></label>
                    <div class="col-sm-6">
                      <select [value]="theme.sportsMobileLayout?.layout" select2 (onSelect)="selectMobileSportLayout($event)"
                        class="form-control" id="sportlayout"
                        [attr.data-placeholder]="'select_layout' | labelName" style="width: 100%;">
                        <option *ngFor="let layouts of mobileSportsLandingPageLayouts" [value]="layouts.value"> {{ layouts.name }}
                        </option>
                      </select>
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="inputEmail3" class="col-sm-6 col-form-label">{{'primary_button_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.primary.main" name="primary_btn_color" id="inputEmail3">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="pbt" class="col-sm-6 col-form-label">{{'primary_button_text' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.primary.contrast_text" name="primary_btn_txt_color" id="pbt">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sbc" class="col-sm-6 col-form-label">{{'secondary_button_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.secondary.main" name="secondary_btn_color" id="sbc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sbt" class="col-sm-6 col-form-label">{{'secondary_button_text' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.secondary.contrast_text" name="secondary_btn_txt_color" id="sbt">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="pbc" class="col-sm-6 col-form-label">{{'page_background_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.default" name="bgd_color" id="pbc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="pbc" class="col-sm-6 col-form-label">{{'navbar_background_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.sub_navbar" name="nvbc_color" id="nvbc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="pbc" class="col-sm-6 col-form-label">{{'navbar_background_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.sub_navbar" name="nvtbc_color" id="nvtbc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sbcr" class="col-sm-6 col-form-label">{{'section_background_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.paper" name="bgp_color" id="sbcr">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sbcr" class="col-sm-6 col-form-label">{{'section_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.paper" name="bgp_text_color" id="sbtc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="ptcr" class="col-sm-6 col-form-label">{{'primary_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.primary" name="primary_text_color" id="ptcr">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="stcr" class="col-sm-6 col-form-label">{{'secondary_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.secondary" name="secondary_text_color" id="stcr">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="dtcr" class="col-sm-6 col-form-label">{{'disabled_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.disabled" name="disabled_text_color" id="dtcr">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'selected_item_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.action.selected" name="selected_color" id="sicr">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'default_alert_message_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.alert.text.default" name="alert_default_color" id="damtc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'success_alert_message_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.alert.text.success" name="alert_success_color" id="samtc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'error_alert_message_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.alert.text.error" name="alert_error_color" id="eamtc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'top_header_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.header" name="header_top_color" id="htc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'top_header_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.header" name="header_top_text_color" id="htc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'page_menu_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.page_menu" name="page_menu_background_color" id="pmc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'page_menu_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.page_menu" name="page_menu_text_color" id="pmtc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'promotion_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.promotion" name="promotion_card_color" id="pcc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'promotion_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.promotion" name="promotion_card_text" id="pct">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'game_card_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.game_card" name="game_card_color" id="gcc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'game_card_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.game_card" name="game_card_text" id="=gct">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'sign_up_pop_up_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.signup_popup" name="sign_up_color" id="gcc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'sign_up_pop_up_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.signup_popup" name="sign_up_text" id="=gct">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'other_pop_up_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.other_popup" name="other_pop_up_color" id="occ">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'other_pop_up_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.other_popup" name="other_pop_up_text" id="=oct">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'announcement_background_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.announcement" name="announcement_bg_color" id="abc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'announcement_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.announcement" name="announcement_text" id="=act">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'featured_background_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.featured" name="featured_bg_color" id="febc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'featured_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.featured" name="featured_text_color" id="fetc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'footer_background_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.footer" name="footer_background_color" id="fbc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'footer_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.footer" name="footer_text_color" id="ftc">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'mobile_footer_navbar_background' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.mobile_footer" name="mobile_footer_background_color"
                        id="mfnb">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'mobile_footer_navbar_text' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.mobile_footer" name="mobile_footer_text_color" id="mfnt">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="sicr" class="col-sm-6 col-form-label">{{'mobile_footer_navbar_action' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.action.focus" name="mobile_footer_action_color" id="mfna">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="muwhtc" class="col-sm-6 col-form-label">{{'mobile_user_wallet_header_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.user_info_mobile_text"
                        name="mobile_user_wallet_header_text_color" id="muwhtc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="muwhbc" class="col-sm-6 col-form-label">Mobile user wallet header background
                      color</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.user_info_mobile"
                        name="mobile_user_wallet_header_background_color" id="muwhbc">
                    </div>
                  </div>

                  <div class="form-group row">
                    <label for="input_color" class="col-sm-6 col-form-label">{{'input_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.background.input_color" name="input_color"
                        id="input_color">
                    </div>
                  </div>
                  <div class="form-group row">
                    <label for="input_text_color"class="col-sm-6 col-form-label">{{'input_text_color' | labelName}}</label>
                    <div class="col-sm-6">
                      <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                        [(ngModel)]="theme.palette.text.input_text_color" name="input_text_color"
                         id="input_text_color">
                    </div>
                  </div>

                  <div class="form-group row mb-0">
                    <label for="font_family" class="col-sm-6 col-form-label">{{'font_family' | labelName}}</label>
                    <div class="col-sm-6">
                      <select [value]="theme.typography.font_family" select2 (onSelect)="selectFont($event)"
                        class="form-control" [disabled]="theme.name !== disable_theme" id="font_family"
                        [attr.data-placeholder]="'select_a_font_family' | labelName" style="width: 100%;">
                        <option *ngFor="let themeFont of themeFonts" [value]="themeFont.value"> {{ themeFont.name }}
                        </option>
                      </select>
                    </div>
                  </div>

                </form>
              </div>

            </div>
          </div>
          <!-- /.card -->

        </div>

        <div class="col-md-6">
          <div class="card mb-0">
            <div class="card-header">
              <h3 class="card-title">{{'theme_preview' | labelName}}</h3>
            </div>
            <!-- /.card-header -->
            <div class="card-body">

              <div class="ibox-content" *ngIf="selectedLayout == '1'">

                <div class="alert-message-text">
                  <div class="alert alert-light" [style.color]="theme.palette.alert.text.default">{{'default_message' | labelName}}</div>
                  <div class="alert alert-success" [style.color]="theme.palette.alert.text.success">{{'success_message' | labelName}}</div>
                  <div class="alert alert-danger" [style.color]="theme.palette.alert.text.error">{{'error_message' | labelName}}</div>
                </div>

                <div class="">
                  <div class="row row-overwrite">
                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.announcement">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.announcement">User
                        mobile wallet background section</p>
                    </div>

                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background?.user_info_mobile">
                      <p class="text-center preview-text-primary"
                        [style.color]="theme.palette.text?.user_info_mobile_text">{{'user_mobile_wallet_section_text' | labelName}}</p>
                    </div>
                    <div class="col-sm-12 Header box-shadow preview display-flex preview-background-paper"
                      [style.background-color]="theme.palette.background.header">
                      <i class="fa fa-th-large fa-lg black preview-palette-type"></i>
                      <p class="text-center float preview-text-primary" [style.color]="theme.palette.text.primary">{{'header' | labelName}}</p>
                      <div class="buttons display-flex">
                        <a class="preview-primary-main preview-primary-contrast-text"
                          [style.background-color]="theme.palette.primary.main"
                          [style.color]="theme.palette.primary.contrast_text">{{'button_1' | labelName}}</a>
                        <a class="preview-secondary-main preview-secondary-contrast-text"
                          [style.background-color]="theme.palette.secondary.main"
                          [style.color]="theme.palette.secondary.contrast_text">{{'button_2' | labelName}}</a>
                      </div>
                    </div>

                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.default">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">Header
                        Menus</p>
                      <!-- color: Text Primary -->
                    </div>
                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.page_menu">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.page_menu">Page
                        Menus</p>
                      <!-- color: Text Primary -->
                    </div>
                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.sub_navbar">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.sub_navbar">{{'sub_navbar' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>
                    <div class="col-sm-12">
                      <div class="row row-overwrite flex-nowrap">
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.promotion">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.promotion">{{'promotion_1' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.promotion">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.promotion">{{'promotion_2' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.promotion">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.promotion">{{'promotion_3' | labelName}}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- game cards,popup -->
                    <!-- cards -->
                    <div class="col-sm-12">
                      <div class="row row-overwrite flex-nowrap">
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.game_card">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.game_card">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.game_card">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- card end -->
                    <div class="col-sm-12">
                      <div class="row">
                        <div class="col-sm-6">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.signup_popup">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.signup_popup">{{'sign_up_pop_up' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-6">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.other_popup">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.other_popup">{{'other_pop_ups' | labelName}}</p>
                          </div>
                        </div>
                      </div>
                    </div>


                    <!-- end -->


                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.paper">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.paper">{{'navbar' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>

                  </div>
                  <div class="row row-overwrite flex-nowrap">
                    <div class="col-sm-3 height Left box-shadow display-flex preview-background-paper"
                      [style.background-color]="theme.palette.background.default">
                      <!-- background: Background Paper -->
                      <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'left_bar' | labelName}}</p>
                      <!-- color: Text Secondary -->
                    </div>
                    <div class="col-sm-6 height body-content box-shadow preview-background-default"
                      [style.background-color]="theme.palette.background.default">
                      <!-- background: Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">{{'body' | labelName}}</p>
                      <!-- color: Text Primary -->
                      <ul class="list-group">
                        <li class="list-group-item list-item box-shadow preview-text-disabled"
                          [style.color]="theme.palette.text.disabled">{{'disabled' | labelName}}</li>
                        <!-- color: Text Disabled -->
                        <li
                          class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                          [style.color]="theme.palette.secondary.contrast_text"
                          [style.background-color]="theme.palette.action.selected">{{'selected' | labelName}}</li>
                        <li
                          class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                          [style.color]="theme.palette.text.featured"
                          [style.background-color]="theme.palette.background.featured">{{'featured_section' | labelName}}</li>
                        <!-- background: Action Selected color: Secondary Contrast Text -->
                        <li class="list-group-item list-item box-shadow preview-text-secondary"
                          [style.color]="theme.palette.text.secondary">{{'third_item' | labelName}}</li>
                        <li class="list-group-item list-item box-shadow preview-text-secondary"
                          [style.color]="theme.palette.text.secondary">{{'fourth_item' | labelName}}</li>
                        <li class="list-group-item list-item box-shadow preview-text-secondary"
                          [style.color]="theme.palette.text.secondary">{{'fifth_item' | labelName}}</li>
                      </ul>
                      <!-- Input Preview -->
                      <div class="form-group">
                        <input type="text" class="form-control" id="inputPreview" [placeholder]="'input_preview' | labelName"
                          [style.background-color]="theme.palette.background.input_color"
                          [style.color]="theme.palette.text.input_text_color">
                      </div>
                    </div>
                    <div class="col-sm-3 height right-side box-shadow display-flex preview-background-paper"
                      [style.background-color]="theme.palette.background.default">
                      <!-- background: Background Paper -->
                      <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'right_bar' | labelName}}</p>
                      <!-- color: Text Secondary -->
                    </div>
                  </div>

                  <div class="col-sm-12">
                    <div class="row row-overwrite flex-nowrap">
                      <div class="col-sm-3">
                        <div class="nav-bar box-shadow preview display-flex preview-background-default"
                          [style.background-color]="theme.palette.background.footer">
                          <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                        </div>
                      </div>
                      <div class="col-sm-3">
                        <div class="nav-bar box-shadow preview display-flex preview-background-default"
                          [style.background-color]="theme.palette.background.footer">
                          <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                        </div>
                      </div>
                      <div class="col-sm-3">
                        <div class="nav-bar box-shadow preview display-flex preview-background-default"
                          [style.background-color]="theme.palette.background.footer">
                          <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                        </div>
                      </div>
                      <div class="col-sm-3">
                        <div class="nav-bar box-shadow preview display-flex preview-background-default"
                          [style.background-color]="theme.palette.background.footer">
                          <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="row row-overwrite">
                    <div class="col-sm-12 footer box-shadow preview-background-default"
                      [style.background-color]="theme.palette.background.mobile_footer">
                      <!-- background: Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.mobile_footer">{{'mobile_footer_navbar_text' | labelName}}</p>
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.action.focus">{{'mobile_footer_action' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>
                  </div>


                </div>

              </div>



              <div class="ibox-content" *ngIf="selectedLayout == '2' && isInitialized">

                <div class="alert-message-text">
                  <div class="alert alert-light" [style.color]="theme.palette.alert.text.default">{{'default_message' | labelName}}</div>
                  <div class="alert alert-success" [style.color]="theme.palette.alert.text.success">{{'success_message' | labelName}}</div>
                  <div class="alert alert-danger" [style.color]="theme.palette.alert.text.error">{{'error_message' | labelName}}</div>
                </div>

                <div class="border-shadow">
                  <div class="row row-overwrite">
                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.announcement">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.announcement">{{'announcement_section' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>

                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background?.user_info_mobile">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary"
                        [style.color]="theme.palette.text?.user_info_mobile_text">{{'user_mobile_wallet_section_text' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>
                    <div class="col-sm-12 Header box-shadow preview display-flex preview-background-paper"
                      [style.background-color]="theme.palette.background.header">
                      <i class="fa fa-th-large fa-lg black preview-palette-type"></i>
                      <!-- background: Background Paper -->
                      <p class="text-center float preview-text-primary" [style.color]="theme.palette.text.primary">{{'header' | labelName}}</p>
                      <!-- color: Text Primary -->
                      <div class="buttons display-flex">
                        <a class="preview-primary-main preview-primary-contrast-text"
                          [style.background-color]="theme.palette.primary.main"
                          [style.color]="theme.palette.primary.contrast_text">{{'button_1' | labelName}}</a>
                        <!-- background: Primary Main  color: Primary Contrast Text -->
                        <a class="preview-secondary-main preview-secondary-contrast-text"
                          [style.background-color]="theme.palette.secondary.main"
                          [style.color]="theme.palette.secondary.contrast_text">{{'button_2' | labelName}}</a>
                        <!-- background: Secondary Main  color: Secondary Contrast Text -->
                      </div>
                    </div>
                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.default">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">Header
                        Menus</p>
                      <!-- color: Text Primary -->
                    </div>
                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.page_menu">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.page_menu">Page
                        Menus</p>
                      <!-- color: Text Primary -->
                    </div>
                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.sub_navbar">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.sub_navbar">{{'sub_navbar' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>

                    <!-- cards -->
                    <div class="col-sm-12">
                      <div class="row row-overwrite flex-nowrap">
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.game_card">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.game_card">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-4">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.game_card">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- card end -->
                    <div class="col-sm-12">
                      <div class="row">
                        <div class="col-sm-6">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.signup_popup">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.signup_popup">{{'sign_up_pop_up' | labelName}}</p>
                          </div>
                        </div>
                        <div class="col-sm-6">
                          <div class="nav-bar box-shadow preview display-flex preview-background-default"
                            [style.background-color]="theme.palette.background.other_popup">
                            <p class="text-center preview-text-primary" [style.color]="theme.palette.text.other_popup">{{'other_pop_ups' | labelName}}</p>
                          </div>
                        </div>
                      </div>
                    </div>


                    <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.paper">
                      <!-- Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.paper">{{'navbar' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>

                  </div>
                  <div class="row row-overwrite flex-nowrap">
                    <div class="col-sm-3 height Left box-shadow display-flex preview-background-paper"
                      [style.background-color]="theme.palette.background.default">
                      <!-- background: Background Paper -->
                      <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'left_bar' | labelName}}</p>
                      <!-- color: Text Secondary -->
                    </div>
                    <div class="col-sm-6 height body-content box-shadow preview-background-default"
                      [style.background-color]="theme.palette.background.default">
                      <!-- background: Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">{{'body' | labelName}}</p>
                      <!-- color: Text Primary -->
                      <ul class="list-group">
                        <li class="list-group-item list-item box-shadow preview-text-disabled"
                          [style.color]="theme.palette.text.disabled">{{'disabled' | labelName}}</li>
                        <!-- color: Text Disabled -->
                        <li
                          class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                          [style.color]="theme.palette.secondary.contrast_text"
                          [style.background-color]="theme.palette.action.selected">{{'selected' | labelName}}</li>
                        <li
                          class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                          [style.color]="theme.palette.text.featured"
                          [style.background-color]="theme.palette.background.featured">{{'featured_section' | labelName}}</li>
                        <!-- background: Action Selected color: Secondary Contrast Text -->
                        <li class="list-group-item list-item box-shadow preview-text-secondary"
                          [style.color]="theme.palette.text.secondary">{{'third_item' | labelName}}</li>
                        <li class="list-group-item list-item box-shadow preview-text-secondary"
                          [style.color]="theme.palette.text.secondary">{{'fourth_item' | labelName}}</li>
                        <li class="list-group-item list-item box-shadow preview-text-secondary"
                          [style.color]="theme.palette.text.secondary">{{'fifth_item' | labelName}}</li>
                      </ul>
                      <!-- Input Preview -->
                      <div class="form-group">
                        <input type="text" class="form-control" id="inputPreview" [placeholder]="'input_preview' | labelName"
                          [style.background-color]="theme.palette.background.input_color"
                          [style.color]="theme.palette.text.input_text_color">
                      </div>
                    </div>
                    <div class="col-sm-3 height right-side box-shadow display-flex preview-background-paper"
                      [style.background-color]="theme.palette.background.default">
                      <!-- background: Background Paper -->
                      <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'right_bar' | labelName}}</p>
                      <!-- color: Text Secondary -->
                    </div>
                  </div>
                  <div class="row row-overwrite">
                    <div class="col-sm-12 footer box-shadow preview-background-default"
                      [style.background-color]="theme.palette.background.footer">
                      <!-- background: Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>
                  </div>
                  <div class="row row-overwrite">
                    <div class="col-sm-12 footer box-shadow preview-background-default"
                      [style.background-color]="theme.palette.background.mobile_footer">
                      <!-- background: Background Default -->
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.mobile_footer">{{'mobile_footer_navbar_text' | labelName}}</p>
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.action.focus">{{'mobile_footer_action' | labelName}}</p>
                      <!-- color: Text Primary -->
                    </div>
                  </div>
                </div>

              </div>




            </div>
          </div>
          <!-- /.card -->

        </div>

      </div>
    </div>
  </div>

</div>

<!-- Accordians End -->

<div class="row custom-row theme-detail-row customFG  mb-4 d-none">
  <div class="col-md-6">
    <div class="card mb-0">
      <div class="card-header">
        <h3 class="card-title">Theme Detail ({{ theme ? theme.name:'' }}) </h3>
      </div>
      <div class="card-body">

        <div class="ibox float-e-margins">

          <form #form="ngForm">

            <div class="form-group row">
              <label for="inputEmail3" class="col-sm-6 col-form-label">{{'theme_type' | labelName}}</label>
              <div class="col-sm-6">
                <div class="form-group clearfix">
                  <div class="icheck-primary d-inline">
                    <input type="radio" id="themeTypeLight2" (change)="changeThemeType('light')" value="light"
                      name="theme_type" [(ngModel)]="theme.palette.type" [disabled]="theme.name !== disable_theme" />
                    <label for="themeTypeLight2">{{'light' | labelName}}</label>
                  </div>
                  &nbsp;
                  <div class="icheck-primary d-inline">
                    <input type="radio" id="themeTypeDark2" value="dark" (change)="changeThemeType('dark')"
                      [(ngModel)]="theme.palette.type" name="theme_type" [disabled]="theme.name !== disable_theme" />
                    <label for="themeTypeDark2">{{'dark' | labelName}}</label>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group row">
              <label for="sportlayout" class="col-sm-6 col-form-label">{{'select_sports_landing_page_layout' | labelName}}<small>{{'for_mobile' | labelName}}</small></label>
              <div class="col-sm-6">
                <select [value]="theme.sportsMobileLayout?.layout" select2 (onSelect)="selectMobileSportLayout($event)"
                  class="form-control" id="sportlayout"
                  [attr.data-placeholder]="'select_layout' | labelName" style="width: 100%;">
                  <option *ngFor="let layouts of mobileSportsLandingPageLayouts" [value]="layouts.value"> {{ layouts.name }}
                  </option>
                </select>
              </div>
            </div>

            <div class="form-group row">
              <label for="inputEmail3" class="col-sm-6 col-form-label">{{'primary_button_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.primary.main" name="primary_btn_color" id="inputEmail3">
              </div>
            </div>

            <div class="form-group row">
              <label for="pbt" class="col-sm-6 col-form-label">{{'primary_button_text' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.primary.contrast_text" name="primary_btn_txt_color" id="pbt">
              </div>
            </div>

            <div class="form-group row">
              <label for="sbc" class="col-sm-6 col-form-label">{{'secondary_button_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.secondary.main" name="secondary_btn_color" id="sbc">
              </div>
            </div>

            <div class="form-group row">
              <label for="sbt" class="col-sm-6 col-form-label">{{'secondary_button_text' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.secondary.contrast_text" name="secondary_btn_txt_color" id="sbt">
              </div>
            </div>

            <div class="form-group row">
              <label for="pbc" class="col-sm-6 col-form-label">{{'page_background_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.default" name="bgd_color" id="pbc">
              </div>
            </div>
            <div class="form-group row">
              <label for="pbc" class="col-sm-6 col-form-label">{{'navbar_background_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.sub_navbar" name="nvbc_color" id="nvbc">
              </div>
            </div>
            <div class="form-group row">
              <label for="pbc" class="col-sm-6 col-form-label">{{'navbar_background_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.sub_navbar" name="nvtbc_color" id="nvtbc">
              </div>
            </div>

            <div class="form-group row">
              <label for="sbcr" class="col-sm-6 col-form-label">{{'section_background_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.paper" name="bgp_color" id="sbcr">
              </div>
            </div>
            <div class="form-group row">
              <label for="sbcr" class="col-sm-6 col-form-label">{{'section_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.paper" name="bgp_text_color" id="sbtc">
              </div>
            </div>

            <div class="form-group row">
              <label for="ptcr" class="col-sm-6 col-form-label">{{'primary_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.primary" name="primary_text_color" id="ptcr">
              </div>
            </div>

            <div class="form-group row">
              <label for="stcr" class="col-sm-6 col-form-label">{{'secondary_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.secondary" name="secondary_text_color" id="stcr">
              </div>
            </div>

            <div class="form-group row">
              <label for="dtcr" class="col-sm-6 col-form-label">{{'disabled_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.disabled" name="disabled_text_color" id="dtcr">
              </div>
            </div>

            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'selected_item_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.action.selected" name="selected_color" id="sicr">
              </div>
            </div>

            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'default_alert_message_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.alert.text.default" name="alert_default_color" id="damtc">
              </div>
            </div>

            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'success_alert_message_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.alert.text.success" name="alert_success_color" id="samtc">
              </div>
            </div>

            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'error_alert_message_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.alert.text.error" name="alert_error_color" id="eamtc">
              </div>
            </div>

            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'top_header_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.header" name="header_top_color" id="htc">
              </div>
            </div>

            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'top_header_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.header" name="header_top_text_color" id="htc">
              </div>
            </div>

            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'page_menu_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.page_menu" name="page_menu_background_color" id="pmc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'page_menu_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.page_menu" name="page_menu_text_color" id="pmtc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'promotion_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.promotion" name="promotion_card_color" id="pcc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'promotion_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.promotion" name="promotion_card_text" id="pct">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'game_card_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.game_card" name="game_card_color" id="gcc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'game_card_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.game_card" name="game_card_text" id="=gct">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'sign_up_pop_up_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.signup_popup" name="sign_up_color" id="gcc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'sign_up_pop_up_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.signup_popup" name="sign_up_text" id="=gct">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'other_pop_up_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.other_popup" name="other_pop_up_color" id="occ">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'other_pop_up_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.other_popup" name="other_pop_up_text" id="=oct">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'announcement_background_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.announcement" name="announcement_bg_color" id="abc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'announcement_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.announcement" name="announcement_text" id="=act">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'featured_background_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.featured" name="featured_bg_color" id="febc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'featured_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.featured" name="featured_text_color" id="fetc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'footer_background_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.footer" name="footer_background_color" id="fbc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'footer_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.footer" name="footer_text_color" id="ftc">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'mobile_footer_navbar_background' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.mobile_footer" name="mobile_footer_background_color" id="mfnb">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'mobile_footer_navbar_text' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.mobile_footer" name="mobile_footer_text_color" id="mfnt">
              </div>
            </div>
            <div class="form-group row">
              <label for="sicr" class="col-sm-6 col-form-label">{{'mobile_footer_navbar_action' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.action.focus" name="mobile_footer_action_color" id="mfna">
              </div>
            </div>

            <div class="form-group row">
              <label for="muwhtc" class="col-sm-6 col-form-label">{{'mobile_user_wallet_header_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.user_info_mobile_text" name="mobile_user_wallet_header_text_color"
                  id="muwhtc">
              </div>
            </div>

            <div class="form-group row">
              <label for="muwhbc" class="col-sm-6 col-form-label">{{'mobile_user_wallet_header_background_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.user_info_mobile"
                  name="mobile_user_wallet_header_background_color" id="muwhbc">
              </div>
            </div>

            <div class="form-group row">
              <label for="input_color" class="col-sm-6 col-form-label">{{'input_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.background.input_color" name="input_color" id="input_color">
              </div>
            </div>
            <div class="form-group row">
              <label for="input_text_color" class="col-sm-6 col-form-label">{{'input_text_color' | labelName}}</label>
              <div class="col-sm-6">
                <input type="color" class="form-control" [disabled]="theme.name !== disable_theme"
                  [(ngModel)]="theme.palette.text.input_text_color" name="input_text_color" id="input_text_color">
              </div>
            </div>

            <div class="form-group row">
              <label for="font_family" class="col-sm-6 col-form-label">{{'font_family' | labelName}}</label>
              <div class="col-sm-6">
                <select [value]="theme.typography.font_family" select2 (onSelect)="selectFont($event)"
                  class="form-control" [disabled]="theme.name !== disable_theme" id="font_family"
                  [attr.data-placeholder]="'select_a_font_family' | labelName" style="width: 100%;">
                  <option *ngFor="let themeFont of themeFonts" [value]="themeFont.value"> {{ themeFont.name }} </option>
                </select>
              </div>
            </div>

          </form>
        </div>

      </div>
    </div>

  </div>

  <div class="col-md-6">
    <div class="card mb-0">
      <div class="card-header">
        <h3 class="card-title">{{'theme_preview' | labelName}}</h3>
      </div>
      <div class="card-body">
        <div *ngIf="!isInitialized" class="text-center py-5">
          <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
            <span class="sr-only">{{'loading' | labelName}}</span>
          </div>
          <p class="mt-3">{{'loading_theme_preview' | labelName}}</p>
        </div>

        <div class="ibox-content" *ngIf="selectedLayout == '1' && isInitialized">

          <div class="alert-message-text">
            <div class="alert alert-light" [style.color]="theme.palette.alert.text.default">{{'default_message' | labelName}}</div>
            <div class="alert alert-success" [style.color]="theme.palette.alert.text.success">{{'success_message' | labelName}}</div>
            <div class="alert alert-danger" [style.color]="theme.palette.alert.text.error">{{'error_message' | labelName}}</div>
          </div>

          <div class="">
            <div class="row row-overwrite">
              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.announcement">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.announcement">User mobile
                  wallet background section</p>

              </div>

              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background?.user_info_mobile">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text?.user_info_mobile_text">{{'user_mobile_wallet_section_text' | labelName}}</p>

              </div>
              <div class="col-sm-12 Header box-shadow preview display-flex preview-background-paper"
                [style.background-color]="theme.palette.background.header">
                <i class="fa fa-th-large fa-lg black preview-palette-type"></i>

                <p class="text-center float preview-text-primary" [style.color]="theme.palette.text.primary">{{'header' | labelName}}</p>

                <div class="buttons display-flex">
                  <a class="preview-primary-main preview-primary-contrast-text"
                    [style.background-color]="theme.palette.primary.main"
                    [style.color]="theme.palette.primary.contrast_text">{{'button_1' | labelName}}</a>

                  <a class="preview-secondary-main preview-secondary-contrast-text"
                    [style.background-color]="theme.palette.secondary.main"
                    [style.color]="theme.palette.secondary.contrast_text">{{'button_2' | labelName}}</a>

                </div>
              </div>

              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.default">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">{{'header_menus' | labelName}}</p>
              </div>
              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.page_menu">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.page_menu">{{'page_menus' | labelName}}</p>
              </div>
              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.sub_navbar">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.sub_navbar">{{'sub_navbar' | labelName}}</p>
              </div>
              <div class="col-sm-12">
                <div class="row row-overwrite flex-nowrap">
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.promotion">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.promotion">Promotion
                        1</p>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.promotion">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.promotion">Promotion
                        2</p>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.promotion">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.promotion">Promotion
                        3</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12">
                <div class="row row-overwrite flex-nowrap">
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.game_card">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.game_card">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.game_card">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12">
                <div class="row">
                  <div class="col-sm-6">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.signup_popup">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.signup_popup">Sign
                        Up Pop Up</p>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.other_popup">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.other_popup">Other
                        Pop Ups</p>
                    </div>
                  </div>
                </div>
              </div>




              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.paper">

                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.paper">{{'navbar' | labelName}}</p>

              </div>

            </div>
            <div class="row row-overwrite flex-nowrap">
              <div class="col-sm-3 height Left box-shadow display-flex preview-background-paper"
                [style.background-color]="theme.palette.background.default">

                <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'left_bar' | labelName}}</p>

              </div>
              <div class="col-sm-6 height body-content box-shadow preview-background-default"
                [style.background-color]="theme.palette.background.default">

                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">{{'body' | labelName}}</p>

                <ul class="list-group">
                  <li class="list-group-item list-item box-shadow preview-text-disabled"
                    [style.color]="theme.palette.text.disabled">{{'disabled' | labelName}}</li>

                  <li
                    class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                    [style.color]="theme.palette.secondary.contrast_text"
                    [style.background-color]="theme.palette.action.selected">{{'selected' | labelName}}</li>
                  <li
                    class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                    [style.color]="theme.palette.text.featured"
                    [style.background-color]="theme.palette.background.featured">{{'featured_section' | labelName}}</li>

                  <li class="list-group-item list-item box-shadow preview-text-secondary"
                    [style.color]="theme.palette.text.secondary">{{'third_item' | labelName}}</li>
                  <li class="list-group-item list-item box-shadow preview-text-secondary"
                    [style.color]="theme.palette.text.secondary">{{'fourth_item' | labelName}}</li>
                  <li class="list-group-item list-item box-shadow preview-text-secondary"
                    [style.color]="theme.palette.text.secondary">{{'fifth_item' | labelName}}</li>
                </ul>

                <div class="form-group">
                  <input type="text" class="form-control" id="inputPreview" [placeholder]="'input_preview' | labelName"
                    [style.background-color]="theme.palette.background.input_color"
                    [style.color]="theme.palette.text.input_text_color">
                </div>
              </div>
              <div class="col-sm-3 height right-side box-shadow display-flex preview-background-paper"
                [style.background-color]="theme.palette.background.default">

                <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'right_bar' | labelName}}</p>

              </div>
            </div>

            <div class="col-sm-12">
              <div class="row row-overwrite flex-nowrap">
                <div class="col-sm-3">
                  <div class="nav-bar box-shadow preview display-flex preview-background-default"
                    [style.background-color]="theme.palette.background.footer">
                    <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                  </div>
                </div>
                <div class="col-sm-3">
                  <div class="nav-bar box-shadow preview display-flex preview-background-default"
                    [style.background-color]="theme.palette.background.footer">
                    <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                  </div>
                </div>
                <div class="col-sm-3">
                  <div class="nav-bar box-shadow preview display-flex preview-background-default"
                    [style.background-color]="theme.palette.background.footer">
                    <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                  </div>
                </div>
                <div class="col-sm-3">
                  <div class="nav-bar box-shadow preview display-flex preview-background-default"
                    [style.background-color]="theme.palette.background.footer">
                    <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
                  </div>
                </div>
              </div>
            </div>


            <div class="row row-overwrite">
              <div class="col-sm-12 footer box-shadow preview-background-default"
                [style.background-color]="theme.palette.background.mobile_footer">

                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.mobile_footer">Mobile
                  Footer Navbar text</p>
                <p class="text-center preview-text-primary" [style.color]="theme.palette.action.focus">{{'mobile_footer_action' | labelName}}</p>

              </div>
            </div>
          </div>

        </div>



        <div class="ibox-content" *ngIf="selectedLayout == '2' && isInitialized">

          <div class="alert-message-text">
            <div class="alert alert-light" [style.color]="theme.palette.alert.text.default">{{'default_message' | labelName}}</div>
            <div class="alert alert-success" [style.color]="theme.palette.alert.text.success">{{'success_message' | labelName}}</div>
            <div class="alert alert-danger" [style.color]="theme.palette.alert.text.error">{{'error_message' | labelName}}</div>
          </div>

          <div class="border-shadow">
            <div class="row row-overwrite">
              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.announcement">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.announcement">{{'announcement_section' | labelName}}</p>
              </div>

              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background?.user_info_mobile">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text?.user_info_mobile_text">{{'user_mobile_wallet_section_text' | labelName}}</p>
              </div>
              <div class="col-sm-12 Header box-shadow preview display-flex preview-background-paper"
                [style.background-color]="theme.palette.background.header">
                <i class="fa fa-th-large fa-lg black preview-palette-type"></i>
                <p class="text-center float preview-text-primary" [style.color]="theme.palette.text.primary">{{'header' | labelName}}</p>
                <div class="buttons display-flex">
                  <a class="preview-primary-main preview-primary-contrast-text"
                    [style.background-color]="theme.palette.primary.main"
                    [style.color]="theme.palette.primary.contrast_text">{{'button_1' | labelName}}</a>
                  <a class="preview-secondary-main preview-secondary-contrast-text"
                    [style.background-color]="theme.palette.secondary.main"
                    [style.color]="theme.palette.secondary.contrast_text">{{'button_2' | labelName}}</a>
                </div>
              </div>
              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.default">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">{{'header_menus' | labelName}}</p>
              </div>
              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.page_menu">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.page_menu">{{'page_menus' | labelName}}</p>
              </div>
              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.sub_navbar">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.sub_navbar">{{'sub_navbar' | labelName}}</p>
              </div>

              <div class="col-sm-12">
                <div class="row row-overwrite flex-nowrap">
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.game_card">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.game_card">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.game_card">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.game_card">{{'game_cards' | labelName}}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-12">
                <div class="row">
                  <div class="col-sm-6">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.signup_popup">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.signup_popup">Sign
                        Up Pop Up</p>
                    </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="nav-bar box-shadow preview display-flex preview-background-default"
                      [style.background-color]="theme.palette.background.other_popup">
                      <p class="text-center preview-text-primary" [style.color]="theme.palette.text.other_popup">Other
                        Pop Ups</p>
                    </div>
                  </div>
                </div>
              </div>


              <div class="col-sm-12 nav-bar box-shadow preview display-flex preview-background-default"
                [style.background-color]="theme.palette.background.paper">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.paper">{{'navbar' | labelName}}</p>
              </div>

            </div>
            <div class="row row-overwrite flex-nowrap">
              <div class="col-sm-3 height Left box-shadow display-flex preview-background-paper"
                [style.background-color]="theme.palette.background.default">
                <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'left_bar' | labelName}}</p>
              </div>
              <div class="col-sm-6 height body-content box-shadow preview-background-default"
                [style.background-color]="theme.palette.background.default">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.primary">{{'body' | labelName}}</p>
                <ul class="list-group">
                  <li class="list-group-item list-item box-shadow preview-text-disabled"
                    [style.color]="theme.palette.text.disabled">{{'disabled' | labelName}}</li>
                  <li
                    class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                    [style.color]="theme.palette.secondary.contrast_text"
                    [style.background-color]="theme.palette.action.selected">{{'selected' | labelName}}</li>
                  <li
                    class="list-group-item list-item box-shadow preview-action-selected preview-secondary-contrast-text"
                    [style.color]="theme.palette.text.featured"
                    [style.background-color]="theme.palette.background.featured">{{'featured_section' | labelName}}</li>
                  <li class="list-group-item list-item box-shadow preview-text-secondary"
                    [style.color]="theme.palette.text.secondary">{{'third_item' | labelName}}</li>
                  <li class="list-group-item list-item box-shadow preview-text-secondary"
                    [style.color]="theme.palette.text.secondary">{{'fourth_item' | labelName}}</li>
                  <li class="list-group-item list-item box-shadow preview-text-secondary"
                    [style.color]="theme.palette.text.secondary">{{'fifth_item' | labelName}}</li>
                </ul>
                <div class="form-group">
                  <input type="text" class="form-control" id="inputPreview" [placeholder]="'input_preview' | labelName"
                    [style.background-color]="theme.palette.background.input_color"
                    [style.color]="theme.palette.text.input_text_color">
                </div>
              </div>
              <div class="col-sm-3 height right-side box-shadow display-flex preview-background-paper"
                [style.background-color]="theme.palette.background.default">
                <p class="text-center preview-text-secondary" [style.color]="theme.palette.text.secondary">{{'right_bar' | labelName}}</p>
              </div>
            </div>
            <div class="row row-overwrite">
              <div class="col-sm-12 footer box-shadow preview-background-default"
                [style.background-color]="theme.palette.background.footer">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.footer">{{'footer' | labelName}}</p>
              </div>
            </div>
            <div class="row row-overwrite">
              <div class="col-sm-12 footer box-shadow preview-background-default"
                [style.background-color]="theme.palette.background.mobile_footer">
                <p class="text-center preview-text-primary" [style.color]="theme.palette.text.mobile_footer">Mobile
                  Footer Navbar text</p>
                <p class="text-center preview-text-primary" [style.color]="theme.palette.action.focus">{{'mobile_footer_action' | labelName}}</p>

              </div>
            </div>
          </div>

        </div>




      </div>
    </div>

  </div>

</div>
