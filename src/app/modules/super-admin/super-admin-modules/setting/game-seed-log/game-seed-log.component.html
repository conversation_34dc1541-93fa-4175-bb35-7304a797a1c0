<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <app-breadcrumb title="Game Seed logs"  class="custom-main-heading"
    [breadcrumbs]="breadcrumbs"></app-breadcrumb>
  <!-- /.content-header -->

  <section class="content">
    <div class="container-fluid px-0">

      <div class="row custom-row">

        <div class="col-md-12">
          <div class="card mb-0">
            <div class="card-body">

              <div class="row mx-0 filterRow">
                <div class="col-xxl-9">
                  <div class="row">
                    <div class="col-md">
                      <div class="form-group">
                        <label for="tenant">Status</label>
                        <select class="form-control" id="status" [(ngModel)]="params.status">
                          <option value="">All</option>
                          <option *ngFor="let status of statuses" [value]="status.value">
                            {{ status.label }}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md">
                      <div class="form-group">
                        <label for="provider">Provider</label>
                        <select class="form-control" select2 id="provider_id" title="Search Provider"
                          (onSelect)="providerChange($event)" [(ngModel)]="params.provider_id"
                          data-placeholder="Select Provider">
                          <option value=""> Select Provider </option>
                          <option *ngFor="let provider of providers" [value]="provider.id">{{ provider.name }}</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md"></div>
                    <div class="col-md"></div>
                  </div>
                </div>
                <div class="col-xxl-3 resetSearch">
                  <div class="form-group custom-btn-secondary form-group-head m-0 text-start">
                    <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn"
                    (click)="resetFilter()"><i class="fa fa-refresh"></i> Reset Filter
                  </button>
                    <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn searchBtn fnt-12"
                      (click)="filter()"><i class="fa fa-search"></i> Search
                    </button>
                  </div>
                </div>
                <div class="col-12 px-0" title="Click then Filter Record">
                    <button class="btn custom-primary-btn" type="button" (click)="openCreateModal()">
                      <i class="fas fa-user-plus"></i> Create
                    </button>
                </div>
<!-- 
                <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                  <div class="form-group">
                    <label for="tenant">Status</label>
                    <select class="form-control" id="status" [(ngModel)]="params.status">
                      <option value="">All</option>
                      <option *ngFor="let status of statuses" [value]="status.value">
                        {{ status.label }}
                      </option>
                    </select>
                  </div>
                </div> -->

                <!-- <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                  <div class="form-group">
                    <label for="provider">Provider</label>
                    <select class="form-control" select2 id="provider_id" title="Search Provider"
                      (onSelect)="providerChange($event)" [(ngModel)]="params.provider_id"
                      data-placeholder="Select Provider">
                      <option value=""> Select Provider </option>
                      <option *ngFor="let provider of providers" [value]="provider.id">{{ provider.name }}</option>
                    </select>
                  </div>
                </div> -->

                <!-- <div class="col-12 col-md-6 col-lg-4 col-xl-3" >
                  <div class="form-group">
                    <label for="tenant_id">Tenant</label>
                    <select class="form-control" select2 id="tenant_id" title="Search Tenant"
                      (onSelect)="tenantChange($event)" [(ngModel)]="params.tenant_id"
                      data-placeholder="Select Tenant">
                      <option value=""> Select Tenant </option>
                      <option value="0"> Super Admin </option>
                      <option *ngFor="let tenant of tenantsList" [value]="tenant.id">{{ tenant.name }}</option>
                    </select>
                  </div>
                </div> -->
<!--
                <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                  <div class="form-group">
                    <label for="start_date">Start Date</label>
                    <input
                      autocomplete="off"
                      class="form-control"
                      data-placeholder="Select Start Date"
                      placeholder="Select Start Date"
                      type="text"
                      id="start_date"
                      (onSelect)="startDateChange($event)"
                      date_picker
                      [(ngModel)]="params.start_date"/>
                  </div>
                </div>
                <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                  <div class="form-group">
                    <label for="end_date">End Date</label>
                    <input
                      autocomplete="off"
                      class="form-control"
                      data-placeholder="Select End Date"
                      placeholder="Select End Date"
                      type="text"
                      id="end_date"
                      (onSelect)="endDateChange($event)"
                      date_picker
                      [ngModel]="params.end_date"/>
                  </div>
                </div> -->

                <div class="col-12 mt-16 px-0">
                  <ng-container *ngIf="!isLoader; else loader">

                    <div class="table-responsive custom-table">
                      <table class="table table-bordered">
                        <thead>
                          <tr>
                            <th>
                              <div>
                                ID
                              <app-ordering-arrow [column]="'id'" [params]="params"
                                (changeOrder)="setOrder($event)"></app-ordering-arrow>
                              </div>
                            </th>
                            <th><div>Provider</div></th>
                            <th><div>Tenant</div></th>
                            <th><div>Created At</div></th>
                            <!-- <th><div>IDs</div></th> -->
                            <th><div>Status</div></th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let log of gameSeedLogs | paginate: { itemsPerPage: params.size, currentPage: p, totalItems: total }">
                            <td>{{ log.id }}</td>
                            <td><span class="badge badge-primary">{{ getProviderNameFromIds(log.ids) }}</span></td>
                            <!-- <td>{{ log.updated_at || "-" }}</td> -->
                            <td>
                              <ng-container *ngFor="let name of getTenantNamesFromIds(log.ids).names; let i = index">
                                <span class="badge badge-dark mr-1">
                                  {{ name }}
                                </span>
                              </ng-container>
                            </td>
                            <td>{{ log.created_at || "-" }}</td>

                            <td><span class="" [ngClass]="getClassFromStatus(log.status)">{{ log.status | parseLabel:statuses || "-" }}</span>
                            </td>
                          </tr>
                          <tr *ngIf="firstTimeApiCall && gameSeedLogs && gameSeedLogs.length === 0">
                            <td colspan="7" class="tnodata">No Queue Logs Found</td>
                          </tr>
                          <tr *ngIf="!firstTimeApiCall">
                            <td colspan="7" class="tnodata">Click Search to Load Data</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <div class="custom-pagination">
                      <div class="pagination-item-list">

                        <pagination-controls (pageChange)="pageChanged($event)"
                          (pageChange)="p = $event"></pagination-controls>
                        <select class="form-control" id="size" [(ngModel)]="params.size" (change)="filter()">
                          <option *ngFor="let size of pageSizes" [value]="size.name"> {{ size.name }} </option>
                        </select>
                      </div>
                      <div>
                        Total Record: {{total||0}}
                      </div>
                    </div>

                  </ng-container>

                </div>
              </div>
    

            </div>

          </div>

        </div>


        <div class="col-md-12">

          <!-- /.card -->

        </div>

      </div>

    </div><!-- /.container-fluid -->
  </section>
  <!-- /.content -->
</div>

<!-- Create Game Seed Modal -->
<div class="modal fade custom-modal" id="createGameSeedModal" tabindex="-1" role="dialog" aria-labelledby="createGameSeedModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createGameSeedModalLabel">Create Game Seed Log</h5>
        <!-- <button type="button" class="close" (click)="closeCreateModal()" aria-label="Close">
           <span aria-hidden="true">&times;</span>
        </button> -->
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="create_provider_id">Game Seed Provider
            <span
              class="text-danger">*</span>
          </label>
          <select class="form-control" id="create_provider_id" [(ngModel)]="createParams.provider_id">
            <option value="">Select Provider</option>
            <option *ngFor="let provider of providers" [value]="provider.id">{{ provider.name }}</option>
          </select>
        </div>

        <!-- <div class="form-group">
          <label>Tenant Selection</label>
          <div class="d-flex">
            <div class="form-check mr-3">
              <input class="form-check-input" type="radio" name="createTenantType" id="createAllTenants" value="all"
                [(ngModel)]="selectedTenantType" (change)="tenantTypeChange($event)">
              <label class="form-check-label" for="createAllTenants">
                All Tenants
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" type="radio" name="createTenantType" id="createSpecificTenants" value="specific"
                [(ngModel)]="selectedTenantType" (change)="tenantTypeChange($event)">
              <label class="form-check-label" for="createSpecificTenants">
                Specific Tenants
              </label>
            </div>
          </div>
        </div> -->
      <div class="form-group mb-0">
            <label class="position-static " for="bonus_type">Tenant Selection <span
              class="text-danger">*</span></label><br>
              <!-- <div class="form-check form-check-inline">
                <input formControlName="bonus_type" class="form-check-input" type="radio" id="gameSeedBonusTypeDeposit" [value]="1">
                <label class="form-check-label bg-transparent" for="gameSeedBonusTypeDeposit">Deposit</label>
              </div>
              <div class="form-check form-check-inline">
                <input formControlName="bonus_type" class="form-check-input" type="radio" id="gameSeedBonusTypeCashback" [value]="2">
                <label class="form-check-label bg-transparent" for="gameSeedBonusTypeCashback">Cashback</label>
              </div> -->
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="createTenantType" id="createAllTenants" value="all"
                  [(ngModel)]="selectedTenantType" (change)="tenantTypeChange($event)">
                <label class="form-check-label" for="createAllTenants">
                  All Tenants
                </label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="createTenantType" id="createSpecificTenants" value="specific"
                  [(ngModel)]="selectedTenantType" (change)="tenantTypeChange($event)">
                <label class="form-check-label" for="createSpecificTenants">
                  Specific Tenants
                </label>
            </div>




          </div>



        <div class="form-group multiple-selection-group" *ngIf="selectedTenantType === 'specific'">
          <label for="create_tenant_ids">Source Tenants <span class="ml-1 text-danger">*</span></label>
          <select class="form-control" id="create_tenant_ids" select2 (onSelect)="selectTenants($event)" multiple
            data-placeholder="Select Tenant">
            <option value="">Select Tenant</option>
            <!-- <option value="0">Super Admin</option> -->
            <option *ngFor="let tenant of tenantsList" [value]="tenant.id">{{ tenant.name }}</option>
          </select>

          <div class="invalid-feedback" *ngIf="submitted && (!createParams.tenant_ids || createParams.tenant_ids.length === 0)">
            <div>Source Tenant is required</div>
          </div>
        </div>

      </div>

      <div class="modal-footer justify-content-between">
          <button type="button" class="btn custom-primary-btn" data-dismiss="modal"
            (click)="closeCreateModal()">Close</button>
          <button type="submit" class="custom-primary-btn" (click)="createGameSeedLog()">
            <i class="fas fa-user-plus"></i>
            Create </button>
        </div>
    </div>
  </div>
</div>

<ng-template #loader>
  <div class="w-full text-center custom-loader-div d-flex justify-content-center mt-4">
    <div class="custom-loader"></div>
  </div>
</ng-template>
