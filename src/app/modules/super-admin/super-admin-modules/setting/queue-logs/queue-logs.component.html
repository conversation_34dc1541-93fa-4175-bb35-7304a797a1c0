<div class="content-wrapper">
  <app-breadcrumb [breadcrumbs]="breadcrumbs" class="custom-main-heading"
                  title="Queue Logs"></app-breadcrumb>
  <section class="content custom-content px-1 px-md-4">
    <div class="container-fluid px-0">
      <div class="row custom-row">
        <div class="col-md-12">
          <div class="card mb-0">
            <ng-container *ngIf="this.permissionService.isSuperAdmin">
              <div *ngIf=" filterGroups && filterGroups.length > 0 ; " class="card-header p-0 mb-2">
                <div class="row w-100 d-flex justify-content-between align-items-center">
                  <div class="form-group col-12 col-sm-6 col-lg-3 col-xl-3 selectTenantCol">
                    <label for="tenant_id">{{'select_tenant' | labelName}}</label>
                    <select (onSelect)="tenantFilter($event)" [(ngModel)]="params.tenant_id" [attr.data-placeholder]="'select' | labelName"
                            id="tenant_id" select2
                            style="width: 100%;">
                      <option [value]="0">{{'all_tenants' | labelName}}</option>
                      <option *ngFor="let tenant of tenantsList" [value]="tenant.id">
                        {{ tenant.name }}
                      </option>
                    </select>
                  </div>


                </div>

              </div>
            </ng-container>


            <div class="card-body">
              <div (click)="getAllFilters()" class="row mx-0 filterRow">
                <div class="col-xxl-9">
                  <div *ngIf=" filterGroups && filterGroups.length > 0 ; " class="row">
                    <ng-container *ngFor="let filter of filterGroups">
                      <ng-container
                        *ngIf="customFilters && customFilters[filter] && customFilters[filter].length  > 0 && filter === 'MAIN_FILTERS'"><ng-container
                          *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: true }"></ng-container>
                      </ng-container>
                    </ng-container>
                  </div>
                </div>
                <div class="col-xxl-3 resetSearch">

                  <div class="form-group custom-btn-secondary form-group-head m-0 text-start">
                    <button (click)="resetFilter()" [disabled]="isLoader" class="btn custom-secondry-btn"
                            type="button"><i class="fa fa-refresh"></i> {{'reset_filter' | labelName}}
                    </button>
                    <button (click)="filter($event)" [disabled]="isLoader"
                            class="btn custom-secondry-btn searchBtn fnt-12" type="button"><i
                      class="fa fa-search"></i> {{'search' | labelName}}
                    </button>
                  </div>
                </div>


                <div class="col-12 px-0">
                  <div class="create-wrapper mb-0">
                    <div class="d-inline-flex align-items-center flex-grow-1 flex-wrap headFiltersRow buttonList"
                         id="buttonList">
                      <ng-container *ngTemplateOutlet="buttonList; context: { buttonType: true }"></ng-container>
                      <div class="buttonId showBtn">

                        <a class="btn btn-warning mb-2 mb-sm-0 btn-sm custom-primary-btn showMore" data-toggle="dropdown"
                           href="#" type="button"
                        >
                        {{'show_more' | labelName}}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right" id="showMore">
                          <div class="dropdown-item">
                            <ng-container *ngTemplateOutlet="buttonList; context: { buttonType: false }"></ng-container>

                          </div>
                        </div>
                      </div>
                    </div>


                  </div>


                </div>
              </div>

            <div class="row px-2 mt-16">


                <ng-container *ngIf="!isLoader; else loader">

                  <div class="table-responsive custom-table hCustom">
                    <table class="table table-bordered">
                      <thead>
                      <tr>
                        <th>
                          <div>
                            {{'id' | labelName}}
                            <app-ordering-arrow (changeOrder)="setOrder($event)" [column]="'id'"
                                                [params]="params"></app-ordering-arrow>
                          </div>
                        </th>
                        <th>
                          <div>{{'created_at' | labelName}}</div>
                        </th>
                        <th>
                          <div>{{'updated_at' | labelName}}</div>
                        </th>
                        <th>
                          <div>{{'type' | labelName}}</div>
                        </th>
                        <th>
                          <div>{{'ids' | labelName}}</div>
                        </th>
                        <th class="centerText">
                          <div>{{'status' | labelName}}</div>
                        </th>
                      </tr>
                      </thead>
                      <tbody>
                      <tr
                        *ngFor="let log of queueLogs | paginate: { itemsPerPage: params.size, currentPage: p, totalItems: total }">
                        <td>{{ log.id }}</td>
                        <td>{{ log.created_at || "-" }}</td>
                        <td>{{ log.updated_at || "-" }}</td>
                        <td>{{ log.type | parseLabel:types || "-" }}</td>
                        <td>{{ log.ids || "-" }}</td>
                        <td class="text-center"><span [ngClass]="getClassFromStatus(log.status)"
                                  class="">{{ log.status | parseLabel:statuses || "-" }}</span>
                        </td>
                      </tr>
                      <tr *ngIf="firstTimeApiCall && queueLogs && queueLogs.length === 0">
                        <td class="tnodata" colspan="7">{{'no_records_found' | labelName}}</td>
                      </tr>
                      <tr *ngIf="!firstTimeApiCall">
                        <td class="tnodata" colspan="7">{{'click_search_to_load_data' | labelName}}</td>
                      </tr>
                      </tbody>
                    </table>
                  </div>

                  <div class="custom-pagination">
                    <div class="pagination-item-list">
                      <pagination-controls (pageChange)="pageChanged($event)"
                                           (pageChange)="p = $event"></pagination-controls>
                      <select (change)="filter($event)" [(ngModel)]="params.size" class="form-control" id="size">
                        <option *ngFor="let size of pageSizes" [value]="size.name">
                          {{ size.name }}
                        </option>
                      </select>
                    </div>
                    <div appPaginationDisplay 
                         [currentPage]="p"
                         [itemsPerPage]="params.size"
                         [totalItems]="total || 0">
                    </div>
                  </div>

                </ng-container>
              </div>
            </div>
          </div>

        </div>

      </div>
      <!-- /.card -->

    </div>
  </section>
  <!-- /.content -->
</div>

<div aria-hidden="true" class="modal fade custom-modal" id="modal-create-queue-log" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">{{'create' | labelName}}</h4>
        <button (click)="closeModal()" aria-label="Close" class="close edit-btn-grp" data-dismiss="modal" type="button">
          <i aria-hidden="true" class="fa fa-times"></i>
        </button>
      </div>

      <form [formGroup]="form">
        <div class="modal-body">

          <div class="form-group">
            <label for="type">{{'type' | labelName}}<span class="ml-1 text-danger">*</span></label>
            <select class="form-control" formControlName="type" id="type">
              <option disabled value="">{{'select' | labelName}}</option>
              <option *ngFor="let type of types" [value]="type.value">{{ type.label }}</option>
            </select>
            <div *ngIf="submitted && f.type.errors" class="invalid-feedback">
              <div *ngIf="f.type.errors.required">{{'type_is_required' | labelName}}</div>
            </div>
          </div>

          <div class="form-group">
            <label class="position-static " for="csv">{{'select_way_to_input_ids' | labelName}}</label>
            <!-- <select class="form-control" id="csv" formControlName="csv">
              <option [value]="true">{{'true' | labelName}}</option>
              <option [value]="false">{{'false' | labelName}}</option>
            </select> -->

            <div>
              <div class="form-check form-check-inline">
                <input [value]="false" class="form-check-input" formControlName="csv" id="queueLogsThroughText" type="radio">
                <label class="form-check-label bg-transparent " for="queueLogsThroughText">through text</label>
              </div>
              <div class="form-check form-check-inline">
                <input [value]="true" class="form-check-input" formControlName="csv" id="queueLogsThroughCsv" type="radio">
                <label class="form-check-label bg-transparent" for="queueLogsThroughCsv">through Csv</label>
              </div>
            </div>
            <div *ngIf="submitted && f.csv.errors" class="invalid-feedback">
              <div *ngIf="f.csv.errors.required">{{'way_is_required' | labelName}}</div>
            </div>
          </div>
          <div *ngIf="!form.get('csv')?.value" class="form-group mb-0">
            <label for="ids">{{'ids' | labelName}}<span class="ml-1 text-danger">*</span>{{'comma_separated' | labelName}}</label>
            <input class="form-control" formControlName="ids" id="ids" [placeholder]="'enter_comma_seperated_ids' | labelName"
                   type="text">
            <div *ngIf="submitted && f.ids.errors" class="invalid-feedback">
              <div *ngIf="f.ids.errors.required">{{'ids_is_required' | labelName}}</div>
              <div *ngIf="f.ids.errors.pattern">Invalid IDs format (only numbers and commas are allowed)</div>
            </div>
          </div>

          <div *ngIf="form.get('csv')?.value" class="form-group upload-img-group mb-0">
            <label class="upload-icon-label" for="csvFile">{{'csv_file' | labelName}}<span class="ml-1 text-danger">*</span>{{'file_should_contain_id_column' | labelName}}</label>
            <div class="input-group">
              <div class="custom-file">
                <input #file (change)="selectCsv(file.files)" accept=".csv" class="custom-file-input" formControlName="csvFile"
                       id="exampleInputFile" type="file">
                <label class="custom-file-label" for="exampleInputFile">{{'choose_file' | labelName}}</label>
              </div>
            </div>
            <div *ngIf="submitted && f.csvFile.errors" class="invalid-feedback">
              <div *ngIf="f.csvFile.errors.required">{{'please_select_a_csv_file' | labelName}}</div>
            </div>

            <div *ngIf="uploadedFileName">
              File Uploaded: {{ uploadedFileName }}
            </div>
          </div>

          <!-- <button class="btn btn-outline-info fnt-12" (click)="sampleCsv()" ><i class="fa fa-download"></i>{{'sample_csv' | labelName}}</button> -->

        </div>
        <div class="modal-footer justify-content-between">
          <button (click)="closeModal()" class="btn custom-btn-warning m-0" data-dismiss="modal" type="button">{{'close' | labelName}}
          </button>
          <button (click)="submitForm()" class="btn custom-primary-btn m-0" type="submit">{{'save_changes' | labelName}}</button>
        </div>

      </form>

    </div>
  </div>
</div>

<ng-template #loader>
  <div class="w-full text-center custom-loader-div d-flex justify-content-center mt-4">
    <!-- <img class="chart-loader" src="./assets/dist/gif/loader.gif" /> -->
    <div class="custom-loader"></div>
  </div>
</ng-template>
<!-- ALL FILTERS USED IN DASHBOARD -->
<ng-template #reusableFilterBlock let-filterKey="filterKey" let-mainFilter="mainFilter">

  <ng-container *ngFor="let item of customFilters && customFilters[filterKey] ? customFilters[filterKey] : []">

    <switch [ngClass]="{ 'col-md ': mainFilter }" [ngSwitch]="item">

      <ng-container *ngSwitchCase="'id'">
        <div [ngClass]="{
                       'select-form': initialFilters[item] != params[item]
                    }" class="form-group">

          <label for="id">{{'by_id' | labelName}}</label>
          <input [(ngModel)]="params.id" class="form-control fnt-14" id="id" [placeholder]="'search' | labelName"
                 type="number"/>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'ids'">
        <div [ngClass]="{
                       'select-form': initialFilters[item] != params[item]
                    }" class="form-group">
          <label for="ids">{{'id' | labelName}}s</label>
          <input [(ngModel)]="params.ids" class="form-control fnt-14" id="ids" [placeholder]="'search' | labelName"
                 type="text"/>
        </div>

      </ng-container>
      <ng-container *ngSwitchCase="'type'">
        <div [ngClass]="{
                       'select-form': initialFilters[item] != params[item]
                    }" class="form-group">
          <label for="type">{{'type' | labelName}}</label>
          <select (onSelect)="typeFilter($event)" [(ngModel)]="params.type" class="form-control"[attr.data-placeholder]="'select' | labelName" id="type"
                  select2>
          <option value="">All</option>
            <option *ngFor="let type of types" [value]="type.value">
              {{ type.label }}
            </option>
          </select>
        </div>

      </ng-container>
      <ng-container *ngSwitchCase="'status'">
        <div [ngClass]="{
                       'select-form': initialFilters[item] != params[item]
                    }" class="form-group">
          <label for="status">{{'status' | labelName}}</label>
          <select [(ngModel)]="params.status" class="form-control" id="status">
          <option value="">All</option>
            <option *ngFor="let status of statuses" [value]="status.value">
              {{ status.label }}
            </option>
          </select>
        </div>

      </ng-container>


      <ng-container *ngSwitchCase="'additional_filter'">
        <!-- <div> -->
        <div class="form-group">
          <div class=" moreBtnGroup" *ngIf="filterGroups && filterGroups.length > 1">
            <button (click)="toggleFilterModal()" class="btn moreFilter" type="button">
              <p>{{'more_filters' | labelName}} <span *ngIf="filterCount != 0" class="filterCount"> {{ filterCount }} </span>
              </p>
              <img alt="filter" src="../../../../assets/dist/svg/filterIcon.svg">
            </button>
          </div>
        </div>

      </ng-container>
      <ng-container *ngSwitchDefault>
      </ng-container>
    </switch>


  </ng-container>

</ng-template>

<!-- filtersModal -->
<filter-modal (apply)="applyFilters()" (close)="toggleFilterModal()" (reset)="resetFilter()"
              [show]="showFilterModal"
              title="Queue Logs">
  <ng-container *ngFor="let filter of filterGroups">
    <ng-container
      *ngIf="customFilters && customFilters[filter] && customFilters[filter].length > 0 && filter !== 'MAIN_FILTERS' && filter !== 'BUTTON'">
      <div class="innerFilterBodyWrap">
        <h6>{{ filter | labelName }}</h6>
        <div class="innerFilterWrap">
          <ng-container
            *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: false }"></ng-container>
        </div>
      </div>
    </ng-container>
  </ng-container>
</filter-modal>


<ng-template #buttonList let-buttonType="buttonType">


  <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'"
  >
    <a (click)="openModal()" class="btn custom-primary-btn mobFull" type="button">
      <i class="fas fa-user-plus"></i>{{'create' | labelName}}</a>

  </div>


</ng-template>
