import { After<PERSON>iewInit, Component, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import { AdminAuthService } from '../../admin/services/admin-auth.service';
import {SuperAdminAuthService} from '../../super-admin/services/super-admin-auth.service';
import {ReportService} from '../reports.service';
import {isDateRangeWithinDuration, PageSizes, TIMEZONE} from 'src/app/shared/constants';
import {Currency} from 'src/app/models';
import { Role } from 'src/app/models';
import Swal from 'sweetalert2';
import { PlayerService } from '../../player/player.service';
declare const $: any;
declare const toastr: any;
import * as moment from 'moment-timezone';
import { PermissionService } from 'src/app/services/permission.service';
import { TenantService } from '../../tenant/tenant.service';
import { domainDetails, getDomainNameFromHost, getSuperTenantsList, isDeepMatch } from 'src/app/services/utils.service';
import { FilterConfig, FilterService } from 'src/app/services/filter.service';
import { BaseFilterComponent } from 'src/app/shared/components/base-filter/base-filter.component';
import { Select2ResetService } from 'src/app/services/select2-reset.service';
@Component({
  selector: 'app-player-db-report',
  templateUrl: './player-db-report.component.html',
  styleUrls: ['./player-db-report.component.scss', '../report.component.scss']
})
export class PlayerDbReportComponent extends BaseFilterComponent  implements OnInit {
  isSubAgentModuleAllowed:boolean = true;
  isPrimaryCurrencyDataEnable:boolean = true;
  roles: string = localStorage.getItem('roles') || '';
  // Properties already defined in BaseFilterComponent are not redeclared:
  // - maxDate, pageSizes, TIMEZONE, zone, isLoader, firstTimeApiCall, secondBackOfficeDomainExistsForTenant, isSuperAdmin

  allColumns:any[]=[];

  selectedColumns:any[]=[
    "all",
    "player_id",
    "player_name",
    "agent_name",
    "country",
    "vip_level",
    "status",
    "current_total_balance",
    "first_deposit_amount",
    "first_deposit_amount_trx_id",
    "first_deposit_timestamp",
    "total_bets",
    "total_bet_amount",
    "player_registration_date",
    "last_login_date",
    "view_user_details"
]

  expanded:boolean=false;

  isAgent:boolean = false;
  reports: any[] = [];
  p: number = 1;
  currenciesResultArray: any[]= [];
  format: string = "YYYY-MM-DD HH:mm:ss";
  currencies: Currency[] = [];
  total: number = 0;
  pageNextTotal: number = 0;
  pageCurrentTotal: number = 0;
  totalBalance: number = 0;
  totalAmountBet: number = 0;
  userList :any[]=[];
  tenantsList :any[]=[];
  tenantsOwnerList :any[]=[];
  totalbetcount: number = 0;
  customDate=false;
  tenant_base_currency:any = '';
  hideShow:boolean = true;
  breadcrumbs: Array<any> = [
      {title: 'Home', path: '/super-admin'},
      {title: 'Player Report', path: '/reports/player'},
  ];
  vipLevelSearch: any;
  disableRequest: boolean = false;
  searchPlaceholder: string;
  reporting_email: any;
  email_verified: boolean = false;
  sameAsOldParams:boolean = true
  oldParams:any = {}
  userRegisteredDateEnabled: boolean = true;
  userFirstDepositDateEnabled: boolean = false;

    initialFilters: any = {
      size: 25,
      page: 1,
      search: '',
      agentId: '',
      dateType: 'today',
      currency: '',
      isDirectPlayer: '',
      timeZone: 'UTC +00:00',
      tenantId: '',
      actionType: '',
      time_period: {},
      timeZoneName: 'UTC',
      vipLevel: [],
      startDate: '',
      endDate: '',
      ownerId: 1,
      order: 'desc',
      sortBy: 'userId',
      playerType : (this.permissionService.checkBotDomain() == false) ? 'all_players' : 'real_players',

      firstTimeDepositDateType: null,
      firstTimeDepositStartDate: null,
      firstTimeDepositEndDate: null,
      firstTimeDepositTimePeriod: {}
    };
    params: any = {...this.initialFilters};
    // Properties already defined in BaseFilterComponent are not redeclared:
    // - customFilters, filterGroups, showFilterModal, filterCount
    

    isLoader:boolean = false;
    firstTimeApiCall = false;
    isSuperAdmin:boolean=this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0;
    secondBackOfficeDomainExistsForTenant : boolean = false;

    constructor(
      private ReportService: ReportService,
      public adminAuthService: AdminAuthService,
      private playerService: PlayerService,
      public superAdminAuthService: SuperAdminAuthService,
      public tenantService: TenantService,
      public select2ResetService: Select2ResetService,
      public override permissionService: PermissionService,
      public override filterService: FilterService
    ) {
      super(filterService, permissionService, adminAuthService, superAdminAuthService);
          let permissions = {
            // Permissions for each field
            "Username": "user_name",
            "Nickname": "nick_name",
            "Player ID": null,
        };

    this.searchPlaceholder = "" + this.modifyKeysBasedOnConditions(permissions).join(' / ')
    this.params = { ...this.params, ...this.ReportService.playerReportReplicaParams };
    this.isSubAgentModuleAllowed = (!!this.permissionService.MODULES?.subAgent ? true : false);
    this.isPrimaryCurrencyDataEnable = (!!this.permissionService.MODULES?.primaryCurrencyDataEnable ? true : false);

    const userData = JSON.parse(localStorage.getItem('userData') || '{}');

    this.reporting_email = userData ? userData.reporting_email : null;
    this.email_verified = userData ? userData.reporting_email_verified : null;

  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.resetFilter(true);
          // Initialize filters
    this.getAllFilters();
    this.columnPermission();

        if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {
            this.getTenantList();
            this.hideShow = false;
        } else {
            // this.getReport();
            this.getCurrencyList();
            this.getAgentUserList();
        }

          if (this.adminAuthService.adminTokenValue && this.adminAuthService.adminTokenValue.length > 0) {

              if(this.roles) {
                const roles = JSON.parse(this.roles);
                if(roles && roles.findIndex( (role: any) => role === Role.Admin ) == -1 && roles.findIndex( (role: any) => role === Role.Agent ) > -1) {
                  this.isAgent = true;
                }

              }

          }

        this.pageNextTotal = this.params.size;
        this.pageCurrentTotal = this.p;
    }

    ngAfterViewInit(): void {
        $('#owner_id option:eq(0)').prop('selected',true);
        setTimeout(() => {
          $('#sb_player_type').val('real_players').trigger('change');
        },10)
    }

    getAgentUserList(){

        if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {

            this.ReportService.getSuperAdminUserList({"owner_id":this.params.ownerId,"tenantId":this.params.tenantId}).subscribe((res: any) => {
                this.userList = res.record;
            });
        } else {
            this.ReportService.getAdminUserList().subscribe((res: any) => {
                this.userList = res.record;
            });
        }

    }

    getTenantList(){

        if(this.superAdminAuthService.superAdminTokenValue &&
            this.superAdminAuthService.superAdminTokenValue.length > 0) {

              this.tenantsList = getSuperTenantsList();
              if (this.tenantsList.length > 0) {
                  this.params.tenantId = 0;
                  this.getTenantOwnerList(0);
                  this.getCurrencyList('0');
                  this.checkSecondBackOfficeDomainExists(this.params.tenantId)
              }
        }
    }

    getTenantOwnerList(id:number) {
        if(this.superAdminAuthService.superAdminTokenValue &&
            this.superAdminAuthService.superAdminTokenValue.length > 0) {
            this.ReportService.getSuperTenantsOwnerList({id}).subscribe((res: any) => {
                this.tenantsOwnerList = res.record;
                this.params.ownerId = this.tenantsOwnerList.length > 0 ? this.tenantsOwnerList[0].id : '';
                $('#owner_id option:eq(0)').prop('selected',true);
                // this.getReport();
                this.disableRequest = false;
            });
        } else {
          this.disableRequest = false;
        }
    }

    tenantFilter( evnt : any ){
        this.disableRequest = true
        this.getTenantOwnerList(evnt);
        this.getCurrencyList(evnt);
        // this.p = 1;
        // this.params.page = this.p;
        this.params.tenantId = parseInt(evnt);
        //this.params.playerType = 'real_players';
        this.checkSecondBackOfficeDomainExists(this.params.tenantId);
        if(evnt != 0){
            this.hideShow = true;
            this.getAgentUserList();
          }
          else{
            this.hideShow = false;
            this.userList = [];
          }
        // this.getReport();
        this.getAllFilters();

    }

    getCurrencyList(evnt=''){
        if(this.superAdminAuthService.superAdminTokenValue &&
            this.superAdminAuthService.superAdminTokenValue.length > 0) {

            this.ReportService.getCurrencyList(evnt).subscribe((res: any) => {
                this.currencies = res.record;
            });

        }else {

            this.ReportService.getCurrencyList().subscribe((res: any) => {
                this.currencies = res.record;
            });
        }
    }

    get getParam(){
        // this.params.ownerId=$('#owner_id').find(':selected');
        // this.params.ownerId=$('#owner_id').select2('data');
        return this.params;
    }
    convertToUTC(inputDate: string, inputTimezone: string): string {
        const date = moment.tz(inputDate, inputTimezone);
        const utcDate = date.utc().format('YYYY-MM-DD HH:mm:ss');
        return utcDate;
      }
    getReport() {
    super.applyFilters(this.params, this.initialFilters, () => {});

      let { hasAccess, noAccessTenantIds } = this.permissionService.managerAllPermissionAvailable(this.params.tenantId, 'player_report', 'R');
      if (!hasAccess) {
        let tenantNames = this.tenantsList.filter((tenant: any) => noAccessTenantIds.includes(tenant.id)).map((tenant: any) => tenant.name).join(', ');
        toastr.error(`You don't have access for ${tenantNames}`);
        return;
      }
      this.filterService.updateTimezone(this.params, this.adminAuthService);
      this.params = {...this.params, timeZoneName: this.params.time_zone_name, timeZone: this.params.time_zone, totals:false}
      let time_period = this.params.time_period
      let time_type = this.params.dateType
      const firstTimeDepositeDateType = this.params.firstTimeDepositDateType;
      const firstTimeDepositTimePeriod = this.params.firstTimeDepositTimePeriod;

      if (firstTimeDepositeDateType == 'custom' && !isDateRangeWithinDuration(firstTimeDepositTimePeriod)) {
        toastr.error('Custom Date should be in range of 90 days');
        return;
      }

      if (time_type== 'custom' && !isDateRangeWithinDuration(time_period)) {
        toastr.error('Custom Date should be in range of 90 days');
        return;
      }
        this.isLoader = true
        this.firstTimeApiCall = true;

        if (
          this.params.time_period?.start_date &&
          this.params.time_period?.end_date &&
          this.params.dateType === 'custom'
        ) {
          this.params = {
            ...this.params,
            startDate: this.params.time_period?.start_date,
            endDate: this.params.time_period?.end_date,
          };
        } else{
          this.params = {
            ...this.params,
            startDate: null,
            endDate: null,
          }
        }

        if (
          this.params.firstTimeDepositTimePeriod?.start_date &&
          this.params.firstTimeDepositTimePeriod?.end_date &&
          this.params.firstTimeDepositDateType === 'custom'
        ) {
          this.params = {
            ...this.params,
            firstTimeDepositStartDate:
              this.params.firstTimeDepositTimePeriod?.start_date,
            firstTimeDepositEndDate:
              this.params.firstTimeDepositTimePeriod?.end_date,
          };
        } else {
          this.params = {
            ...this.params,
            firstTimeDepositStartDate: null,
            firstTimeDepositEndDate: null,
          }
        }

        this.getParam.vipLevel = this.getParam.vipLevel.toString()

        if(this.getParam.agentId == null) this.getParam.agentId = ''
        // delete this.getParam.time_period;
        delete this.getParam.datetime;
        if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {
          this.ReportService.getAdminPlayerReportReplica(this.getParam).subscribe((res: any) => {
              this. getPrepareRecord(res);
              this.isLoader = false
          }, (error:any) => {
          this.isLoader = false
        });
        } else {
          delete this.getParam.tenantId
          this.ReportService.getAdminPlayerReportReplica(this.getParam).subscribe((res: any) => {
              this. getPrepareRecord(res);
              this.isLoader = false
          }, (error:any) => {
          this.isLoader = false
        });
      }
      let modifiedParams = this.getModifiedParams(this.params)
      modifiedParams.totals = false
      if (!this.sameAsOldParams) {
        this.getReportTotal(modifiedParams)
      }
  
    }

    getReportTotal(modifiedParams: any) {
      // // let time_period = this.params.time_period
      // // let time_type = this.params.dateType
      // // let modifiedParams = this.params
      // // modifiedParams.totals = true
      // if (time_type == 'custom' && !isDateRangeWithinDuration(time_period)) {
      //   // toastr.error('Custom Date should be in range of 90 days');
      //   return;
      // }
      modifiedParams = { ...modifiedParams, totals: true }
        if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {
          this.ReportService.getAdminPlayerReportTotalReplica(modifiedParams).subscribe((res: any) => {
              this. getPreparedTotalRecord(res);
              this.isLoader = false
          }, (error:any) => {
          this.isLoader = false
        });
        } else {
          delete this.getParam.tenantId
          this.ReportService.getAdminPlayerReportTotalReplica(modifiedParams).subscribe((res: any) => {
              this. getPreparedTotalRecord(res);
              this.isLoader = false
          }, (error:any) => {
          this.isLoader = false
        });
      }
    }

    getModifiedParams(params: any){
      let modifiedParams = { ...params }
          let modifiedParamswithoutPage = { ...modifiedParams }
          delete modifiedParamswithoutPage.page
          delete modifiedParamswithoutPage.size
          delete modifiedParamswithoutPage.totals
          delete modifiedParamswithoutPage.send_email
          this.sameAsOldParams = isDeepMatch(this.oldParams, modifiedParamswithoutPage)
          this.oldParams = {...modifiedParams}
          delete this.oldParams.page
          delete this.oldParams.size
          delete this.oldParams.totals
          delete this.oldParams.send_email
          return modifiedParams
      }


    getPrepareRecord(res:any){
        this.reports = res.data.data?.result.rows
        // this.total = res.data.data?.result.count;
        this.currenciesResultArray = res.data.data?.currency
        // this.totalAmountBet = res.data.data?.result?.rows[0]?.totalCasinoBetAmount ?? 0;

        let limit = this.params.size;
        let offset = 1;
        if (this.p > 1) {
            offset = limit * (this.p - 1);
        }
        this.pageCurrentTotal = offset;

        this.pageNextTotal = (offset>1?offset:0) + parseInt(limit);
        if(this.total < this.pageNextTotal){
            this.pageNextTotal = this.total;
        }
      this.isLoader = false


    }

    getPreparedTotalRecord(res: any){
      this.totalbetcount = res.data?.data?.result?.totalBetCount ?? 0;
      this.totalBalance = res.data?.data?.result?.totalBalance ?? 0;
      this.tenant_base_currency = res.data.data?.result?.tenantBaseCurrency
      this.total = res.data.data?.result.count;
      this.isLoader = false
    }

    filter(evt: any,dateFilterType: boolean=false) {

        if(this.params.dateType=='custom'){
          this.customDate=true;
          if(dateFilterType){
            setTimeout(() => {
              $('#time_period').val('');
            }, 10);
          }
        //   if(!dateFilterType)
        //   this.getReport();
        }else{
          this.customDate=false;
        }
        this.getAllFilters();
      }

    submitFilter(){
        this.p = 1;
        this.vipLevelSearch = this.params.vipLevel ? this.params.vipLevel.toString() : [];
        this.params = { ...this.params, page: this.p, vipLevel: this.vipLevelSearch || [] };
        if(this.disableRequest){ return }
        this.getReport();
    }

      selectDateRange(time_period: any) {
        this.params = {...this.params, time_period}
        // this.params = {...this.params, startDate: time_period.start_date, endDate: time_period.end_date, time_period}
        // this.getReport();
      }

    filterSelectAgent(evt: any) {
      if(evt == null) return;
      this.params = {...this.params, agentId: (evt == 0) ? '' : evt};
    }

    filterSelectOwner(evt: any) {
      this.params = {...this.params, ownerId:evt};
    }




    pageChanged(page: number) {
        this.params = {...this.params, page};
        if(this.disableRequest){ return }
        this.getReport();
    }

    resetFilter(nodata=false) {
      this.p = 1;
      this.params = { ...this.initialFilters };
     if(!nodata){
      this.getReport();
     }
    setTimeout(() => {
      $( "#owner_id" ).val('1').trigger('change');
      $( "#agent_id" ).val('').trigger('change');
      
      // $( "#time_zone" ).val('UTC +00:00').trigger('change');
      $('#sb_player_type').val(this.params.playerCategory).trigger('change');
      if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0){
        $( "#tenant_id" ).val(0).trigger('change');
      }
      $('#vip_levels').val('').trigger('change');

      $('#first_time_deposit_date_type').val('').trigger('change');
      $('#first_time_deposit_time_period').val('');
    },10)
      this.userFirstDepositDateEnabled = false;
      
      this.disableRequest=false;
      this.getAllFilters();
      super.applyFilters(this.params, this.initialFilters, () => {});
  
    }

    download(action: 'download' | 'email'){
      this.filterService.updateTimezone(this.params, this.adminAuthService);
      this.params = {...this.params, timeZoneName: this.params.time_zone_name, timeZone: this.params.time_zone}
      let module = (action === 'download') ? 'export' : 'send_email';
      let { hasAccess, noAccessTenantIds } = this.permissionService.managerAllPermissionAvailable(this.params.tenantId, 'player_report', module);
      if (!hasAccess) {
        let tenantNames = this.tenantsList.filter((tenant: any) => noAccessTenantIds.includes(tenant.id)).map((tenant: any) => tenant.name).join(', ');
        toastr.error(`You don't have access for ${tenantNames}`);
        return;
      }
      this.filterService.updateTimezone(this.params, this.adminAuthService);


      if (this.params.time_zone) {
        if (action === 'email') {
          this.params = { ...this.params, send_email: true };
        } else {
          this.params = { ...this.params, send_email: false };
        }
      }

      if (
        this.params.time_period?.start_date &&
        this.params.time_period?.end_date &&
        this.params.dateType === 'custom'
      ) {
        this.params = {
          ...this.params,
          startDate: this.params.time_period?.start_date,
          endDate: this.params.time_period?.end_date,
        };
      } else {
        this.params = {
          ...this.params,
          startDate: null,
          endDate: null,
        };
      }

      if (
        this.params.firstTimeDepositTimePeriod?.start_date &&
        this.params.firstTimeDepositTimePeriod?.end_date &&
        this.params.firstTimeDepositDateType === 'custom'
      ) {
        this.params = {
          ...this.params,
          firstTimeDepositStartDate:
            this.params.firstTimeDepositTimePeriod?.start_date,
          firstTimeDepositEndDate:
            this.params.firstTimeDepositTimePeriod?.end_date,
        };
      } else {
        this.params = {
          ...this.params,
          firstTimeDepositStartDate: null,
          firstTimeDepositEndDate: null,
        };
      }


        let data ={
          payload : JSON.stringify(this.params),
          module : 'player_report',
          type : 'player_report_db'
        }
        this.ReportService.export(data).subscribe(
          (res: any) => {
            toastr.success(res.message || 'CSV generation has been successfully started... You can download the file in the export section.')
          });
    }

    updateStatus(report: any, status: number) {
        Swal.fire({
            title: 'Are you sure?',
            text: `You want to ${status == 0 ? 'Deactive' : 'Active'} ${report._source.user_name}!`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: `Yes, ${status == 0 ? 'Deactive' : 'Active'} it!`
            }).then((result) => {
            if (result.isConfirmed) {
                const payload: any = { status };
                this.playerService.updateAdminPlayerStatus(report._source.player_id, payload).subscribe((res: any) => {
                    toastr.success(res.message || 'Player updated successfully' );
                    this.reports = this.reports.map((f: any) => {
                        if(f._source.player_id == report._source.player_id) {
                            f._source.status = status == 0 ? 'Deactive' : 'Active';
                        }
                        return f;
                    });
                });

            }
        });
    }

  setOrder(sort: any) {
    this.p = 1;
    // added sort_by to show the sorting arrow highlighted when clicked
    this.params = { ...this.params, page: this.p, order: sort.order, sortBy: sort.column, sort_by: (sort.columnDB || sort.column)};
    if(this.disableRequest){ return }
    this.getReport();
  }

    ngOnDestroy(): void {
        this.ReportService.playerReportParams = { ...this.ReportService.playerReportParams, ...this.params };
    }

    selectVipLevelSearch(vipLevel: any) {
      this.params= {
        ...this.params,
        vipLevel: vipLevel || []
      }
    }

    getNumArr(length: number) {
      return Array.from({ length }).map((_, i) => i);
     }

     disbleSearch(){
      this.params.time_period=[];
      if(this.params.dateType=='custom' && this.params.time_period.length==0){
        this.disableRequest=true;
      }else{
        this.disableRequest=false;

      }
    }
    updateSearch(){
        this.disableRequest=false;
    }
    sumTotals(total:any, totalSport:any){
      return parseFloat(total) + parseFloat(totalSport)
    }

    modifyKeysBasedOnConditions(attributePermissions:any) {
      for (let attribute in attributePermissions) {
          if (attributePermissions.hasOwnProperty(attribute)) {
              let permissionKey = attributePermissions[attribute];
              if (permissionKey !== null) {
                  if (!this.permissionService.checkPermissionForPlayerKey('players_key',permissionKey)) {
                      delete attributePermissions[attribute];
                  }
              }
          }
      }

    let finalAttributes = Object.keys(attributePermissions);

    return finalAttributes;
    }

  toggleSelection(column: string) {
    let checked = this.isSelected(column);
    if (column == 'ALL' && checked) {
      this.selectedColumns = [];


    } else if (column == 'ALL' && !checked) {
      this.selectedColumns = this.allColumns;
    } else {
      if (!checked) {
        this.selectedColumns.push(column);
        this.selectedColumns.length == (this.allColumns.length - 1) ? this.selectedColumns.push('ALL') : '';
      } else {
        this.selectedColumns.includes('ALL') ? this.selectedColumns = this.selectedColumns.filter(col => col !== 'ALL') : '';
        this.selectedColumns = this.selectedColumns.filter(col => col !== column);
      }
    }
  }

    isSelected(column: string): boolean {
      return this.selectedColumns.includes(column);
    }

    showCheckboxes() {
      var checkboxes = document.getElementById("checkboxes");
      if (checkboxes !== null) {
      if (!this.expanded) {
        checkboxes.style.display = "block";
        this.expanded = true;
      } else {
        checkboxes.style.display = "none";
       this.expanded = false;
      }
    }
  }


  checkSecondBackOfficeDomainExists(tenantId: any) {
    this.tenantService.checkSecondBackOfficeDomainExists({ tenantId: tenantId })
    .subscribe(
      (res: any) => {
       if (res && res.record.sboDomainExists == true && this.superAdminAuthService.superAdminUserValue?.role != 'manager') {
          this.secondBackOfficeDomainExistsForTenant = true;
          setTimeout(function () {
            $('#player_type').val('real_players').trigger('change');
          }, 100);
        }else{
          this.secondBackOfficeDomainExistsForTenant = false;
        }
        ($('#sb_player_type').val(this.params.playerType).trigger('change'));
        this.getAllFilters();
      }
    );
  }
  onPlayerTypeChange(event: any) {
    this.params.playerType = event.value;
  }
  isBot(user: any): boolean {
    return user?.bot_user_id !== undefined ? user?.bot_user_id !== null && user?.bot_user_id !== '' : false;
  }

  selectedColumnsList(event: any) {
    this.selectedColumns = event;
    this.allColumns = this.allColumns.map((player: any) => {
      player.selected = this.selectedColumns.includes(player.name)
      return player;
    })
  }

  columnPermission() {
    this.allColumns = [
      { name: 'all', permission: true, },
      { name: 'player_id', permission: this.permissionService.PERMISSIONS?.report_attributes?.player_id, },
      { name: 'player_name', permission: this.permissionService.PERMISSIONS?.players_key?.user_name, },
      { name: 'agent_name', permission: this.permissionService.PERMISSIONS?.players_key?.agent_details, },
      { name: 'country', permission: this.permissionService.PERMISSIONS?.report_attributes?.country, },
      { name: 'vip_level', permission: this.permissionService.PERMISSIONS?.players_key?.vip_level, },
      { name: 'status', permission: this.permissionService.PERMISSIONS?.report_attributes?.status, },
      { name: 'current_total_balance', permission: this.permissionService.PERMISSIONS?.players_key?.total_balance, },
      { name: 'first_deposit_amount', permission: this.permissionService.PERMISSIONS?.financial_attributes?.first_deposit_amount, },
      { name: 'first_deposit_amount_trx_id', permission: this.permissionService.PERMISSIONS?.financial_attributes?.first_deposit_amount_transaction_id, },
      { name: 'first_deposit_timestamp', permission: this.permissionService.PERMISSIONS?.financial_attributes?.first_deposit_timestamp, },
      { name: 'total_bets', permission: this.permissionService.PERMISSIONS?.report_attributes?.total_bet, },
      { name: 'total_bet_amount', permission: this.permissionService.PERMISSIONS?.report_attributes?.total_bet_amount, },
      { name: 'player_registration_date', permission: true, },
      { name: 'last_login_date', permission: this.permissionService.PERMISSIONS?.players_key?.last_login_date, },
      { name: 'view_user_details', permission: this.permissionService.PERMISSIONS?.players?.R, },
    ];
       // remove from selectedColumns if permission is not there 
    let columnWithPermission = this.allColumns.filter(column => column.permission);
    this.selectedColumns = this.selectedColumns.filter(column => columnWithPermission.some(col => col.name === column));
    if (this.selectedColumns.length) {
      this.allColumns.forEach(column => {
        if (column.permission) {
          column.selected = this.selectedColumns.includes(column.name)
        } else {
          column.selected = false
        }
      })
    }

    return this.allColumns

  }




  /**
   * Applies the current filters to the player list and hides the filter modal.
   *
   * This method uses the base implementation from BaseFilterComponent
   * but provides a custom callback to get the report data.
   */
  applyFilters(): void { 
    super.applyFilters(this.params, this.initialFilters, () => this.submitFilter());
  }


  /**
   * Retrieves and organizes all available filters for the player list component.
   *
   * This method defines a set of filters grouped by categories such as `MAIN_FILTERS`,
   * `ACCOUNT`, `BALANCE_RANGE`, `DATE_RANGE`, `PLAYER`, and `DOCUMENTS`. Each filter
   * includes a key, a group, and a permission condition that determines its availability.
   *
   * The filters are processed using the `permissionService.getFormattedFilters` method,
   * which formats the filters and groups them accordingly. The formatted filters and
   * groups are then assigned to `customFilters` and `filterGroups` respectively.
   *
   * @returns {void} This method does not return a value. It updates the `customFilters`
   * and `filterGroups` properties of the component.
   */
  override getAllFilters(): void {
    const filters = this.getFilters();
   const result = this.filterService.getFormattedFilters(filters, this.filteredKeys);
    this.customFilters = result.customFormat;
    this.filterGroups = result.filterGroups;
         setTimeout(() => {
        this.select2ResetService.initializeMultiSelect(this.params.agentId, 'agent_id')
        this.select2ResetService.initializeMultiSelect(this.params.game_provider, 'game_provider')
        this.select2ResetService.initializeMultiSelect(this.params.vipLevel ? this.params.vipLevel : [], 'vip_levels')
        // this.select2ResetService.initializeMultiSelect(this.params.payment_provider_id, 'payment_provider_id')
      },100)

  }


  // Using the base implementation of getFilterCount from BaseFilterComponent
  // which already handles counting changed filters

  // Using the base implementation of allFilters getter from BaseFilterComponent

  // Using the base implementation of isDeepMatchValues from BaseFilterComponent
  // which already handles deep comparison of arrays

  /**
   * Checks if a filter value has changed from its initial value
   *
   * @param key - The filter key to check
   * @returns True if the filter value has changed, false otherwise
   */
  isFilterChanged(key: string): boolean {
    return super.isFilterChanged(key, this.initialFilters, this.params);
  }

  /**
 * Overrides the toggleFilterModal method from BaseFilterComponent
 * to add custom behavior.
 */
  override toggleFilterModal(): void {
    this.showFilterModal = !this.showFilterModal;
  }


    /**
         *
     * This method implements the abstract method from BaseFilterComponent.
     * It returns an array of filter configurations.
     *
     * @returns {FilterConfig[]} An array of filter configurations.
     */
    override getFilters(): FilterConfig[] {
      return [
        { group: 'PLAYER', key: 'search', permission: true, filterKey: 'search' },
        { group: 'PLAYER', key: 'isDirectPlayer', permission: false, filterKey: 'isDirectPlayer' },
        { group: 'PLAYER', key: 'agentId', permission: this.permissionService.PERMISSIONS?.players_key?.agent_details && this.permissionService.PERMISSIONS?.agents?.R && this.hideShow, filterKey: 'agentId' },
        { group: 'PLAYER', key: 'dateType', permission: true && this.hideShow, filterKey: 'dateType' },
        { group: 'PLAYER', key: 'time_period', permission: this.params.dateType == 'custom', filterKey: 'time_period' },

        { group: 'PLAYER', key: 'currency', permission: true, filterKey: 'currency' },
        { group: 'PLAYER', key: 'actionType', permission: true, filterKey: 'actionType' },
        { group: 'PLAYER', key: 'vipLevel', permission: true, filterKey: 'vipLevel' },
        { group: 'PLAYER', key: 'firstTimeDepositDateType', permission: true  && this.hideShow, filterKey: 'firstTimeDepositDateType' },
        { group: 'PLAYER', key: 'firstTimeDepositTimePeriod', permission: this.params.firstTimeDepositDateType === 'custom', filterKey: 'firstTimeDepositTimePeriod' },
        { group: 'ACCOUNT', key: 'playerType', permission: (this.isSuperAdmin && this.secondBackOfficeDomainExistsForTenant == true) || (this.permissionService.checkBotDomain() == true), filterKey: 'playerType' },
      ];
    }

    filterFirstTimeDepositDate(evt: any, dateFilterType: boolean = false) {
      const targetId = evt.target?.id;
      
      if (targetId === 'first_time_deposit_date_type') {
        this.params.firstTimeDepositDateType = evt.target.value;
      }

      // Handle date filter type
      if (this.params.firstTimeDepositDateType === 'custom') {
        if (dateFilterType) {
          setTimeout(() => {
            $('#first_time_deposit_time_period').val('');
          }, 10);
        }
      }else{
        this.params.firstTimeDepositTimePeriod = {};
        this.params.firstTimeDepositStartDate = null;
        this.params.firstTimeDepositEndDate = null;
      }

      this.getAllFilters();
    }

    selectFirstTimeDepositDateRange(time_period: any) {
      this.params = {
        ...this.params, 
        firstTimeDepositTimePeriod: time_period,
        firstTimeDepositStartDate: time_period.start_date,
        firstTimeDepositEndDate: time_period.end_date
      };
      this.getAllFilters();
    }

    firstTimeDepositDatePickerCheckboxChange(event: any) {
      if (event.target.checked) {
        this.userFirstDepositDateEnabled = true;
        this.params.firstTimeDepositDateType = 'today';
        this.params.firstTimeDepositTimePeriod = {};
        this.params.firstTimeDepositStartDate = null;
        this.params.firstTimeDepositEndDate = null;
      } else {
        this.userFirstDepositDateEnabled = false;
        this.params.firstTimeDepositDateType = null;
        this.params.firstTimeDepositTimePeriod = {};
        this.params.firstTimeDepositStartDate = null;
        this.params.firstTimeDepositEndDate = null;

        if(!this.params.dateType){
          this.params.dateType = 'today';
        }
      }
    }

    userRegisteredDatePickerCheckboxChange(event: any) {
      if (event.target.checked) {
        this.userRegisteredDateEnabled = true;
        this.params.dateType = 'today';
        this.params.time_period = {};
        this.params.startDate = null;
        this.params.endDate = null;
      } else {
        this.userRegisteredDateEnabled = false;
        this.params.dateType = null;
        this.params.time_period = {};
        this.params.startDate = null;
        this.params.endDate = null;

        if(!this.params.firstTimeDepositDateType){
          this.params.firstTimeDepositDateType = 'today';
        }
      }
    }
}
