<div class="content-wrapper">
  <app-breadcrumb title="Player Financial Report"  class="custom-main-heading" [breadcrumbs]="breadcrumbs"></app-breadcrumb>
  <section class="content">
    <div class="container-fluid px-0">
      <div class="row custom-row">
        <div class="col-md-12">
          <div class="card mb-0">
            <ng-container *ngIf="this.permissionService.isSuperAdmin">
              <div class="card-header p-0 mb-2" *ngIf=" filterGroups && filterGroups.length > 0 ; ">
                  <div class="row w-100 d-flex align-items-center mainHeadFilter">
                          <div class="form-group col-12 col-sm-6 col-lg-3 col-xl-3 selectTenantCol">
                              <label for="tenant_id">{{'select_tenant' | labelName}}</label>
                              <select select2 id="tenant_id" style="width: 100%;" [attr.data-placeholder]="'select' | labelName" [(ngModel)]="params.tenant_id"
                                (onSelect)="tenantFilter($event)">
                                <option [value]="0">{{'all_tenants' | labelName}}</option>
                                <option *ngFor="let tenant of tenantsList" [value]="tenant.id">{{tenant.name}}</option>
                            </select>
                            </div>

                      <div class="form-group col-12 col-sm-6 col-lg-3 col-xl-3" *ngIf="hideShow">
                            <label for="owner_id">{{'select_owner' | labelName}}</label>
                            <select class="select2custom" select2 id="owner_id" (onSelect)="filterSelectOwner($event)"
                              [(ngModel)]="params.owner_id" style="width: 325px" [attr.data-placeholder]="'select' | labelName">
                              <!-- <option value="">{{'select_owner' | labelName}}</option> -->
                              <option *ngFor="let tenants of tenantsOwnerList let i = index " [attr.selected]="!i"
                                [value]="tenants.id">{{tenants.agent_name}}</option>
                            </select>

                        </div>
                  </div>
              </div>
            </ng-container>


            <div class="card-body">
              <div class="row mx-0 filterRow">
                <div class="col-xxl-9">
                  <div class="row" *ngIf=" filterGroups && filterGroups.length > 0 ; ">
                    <ng-container *ngFor="let filter of filterGroups">
                      <ng-container *ngIf="customFilters && customFilters[filter] && customFilters[filter].length  > 0 && filter === 'MAIN_FILTERS'"><ng-container
                          *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: true }"></ng-container>
                      </ng-container>
                    </ng-container>
                  </div>
                </div>
                <div class="col-xxl-3 resetSearch">
                  <div class="form-group custom-btn-secondary form-group-head m-0 text-start">
                    <ng-container *ngTemplateOutlet="filtersSearch"></ng-container>
                  </div>
                </div>


                <div class="col-12 px-0">
                  <div class="row rowBtnWrap justify-content-end mx-0">


                    <div class="d-flex justify-content-end align-items-center selectExport pr-0">
                      <ng-container *ngIf="!disableRequest && params.time_type != 'real-time-sync'">
                        <div>

                          <ng-container *ngIf="this.permissionService.checkPermission('casino_player_revenue_report', 'send_email') && (reporting_email && email_verified) && (reports.length > 0)">
                              <button type="button" *ngIf="(reports.length > 0)" class="mgt btn custom-secondry-btn" (click)="download('email')">
                                <i class="fa fa-envelope"></i> {{ 'send_email' | labelName}}
                              </button>
                            </ng-container>
                        </div>

                        <ng-container *ngIf="this.permissionService.checkPermission('player_financial_report','export')">
                          <button type="button" *ngIf="(reports.length > 0)" class="mgt btn custom-secondry-btn"
                            (click)="download('download')"> <i class="fa fa-download"></i> {{'export_as_csv' | labelName}} </button>
                        </ng-container>

                        <div>

                        </div>
                      </ng-container>
                    <app-select-all [allColumns]="allColumns"
                      (selectedColumnsChange)="selectedColumnsList($event)" >
                    </app-select-all>
                    </div>
                  </div>
                  <!-- <div class="inner-custom-total-grid my-3" *ngIf="tenantBaseCurrency != ''">
                    <div class="innerCus-col">
                      <h5>Total Deposit (In {{tenantBaseCurrency}}):</h5>
                      <p>{{(totalDeposit | amountFormat: tenantBaseCurrency : '': false)}}</p>
                    </div>
                    <div class="innerCus-col">
                      <h5>Total Withdraw (In {{tenantBaseCurrency}}):</h5>
                      <p>{{(totalWithdraw | amountFormat: tenantBaseCurrency : '': false)}}</p>
                    </div>
                    <div class="innerCus-col">
                      <h5>Total Withdraw (In {{tenantBaseCurrency}}):</h5>
                      <p>{{(totalWithdraw | amountFormat: tenantBaseCurrency : '': false)}}</p>
                    </div>
                    <div class="innerCus-col">
                      <h5>Total Withdraw Cancel (In {{tenantBaseCurrency}}):</h5>
                      <p>{{(totalWithdrawCancel | amountFormat: tenantBaseCurrency : '': false)}}</p>
                    </div>
                  </div> -->
                  <div class="custom-total-grid6 mt-3" *ngIf="tenantBaseCurrency != '' && reports.length > 0">
                    <div class="ggr-card">
                        <div class="ggr-wrap flex-wrap">
                            <div class="ggr-head">
                                <h5>Total Deposit
                                   <i data-toggle="tooltip"
                                                                title="Total Cash + Non-Cash Deposit + Free deposit + Sports deposit"
                                                                class="fas fa-info-circle c1 mx-1">
                                </i>
                                </h5>
                                <h3>
                                  <span [ngStyle]="{'color': totalDeposit < 0 ? 'red' : ''}"
                                            [innerHTML]="((totalDeposit || 0) | amountFormat: tenantBaseCurrency : '': true: 'h3-currency') "></span>
                                </h3>
                                <!-- <p>Gross Gaming Revenue</p> -->
                            </div>
                            <span class="vertical-seprator"></span>
                            <div class="sub-cash">
                              <h6><span>Total Cash:</span> 
                                  <span [ngStyle]="{'color': totalCashDeposit < 0 ? 'red' : ''}"
                                              [innerHTML]="((totalCashDeposit || 0) | amountFormat: tenantBaseCurrency : '': true) "></span>
                              </h6>
                              <h6><span>Total Non Cash:</span> 
                                  <span [ngStyle]="{'color': totalNonDeposit < 0 ? 'red' : ''}"
                                              [innerHTML]="((totalNonDeposit || 0) | amountFormat: tenantBaseCurrency : '': true) "></span>
                              </h6>
                            </div>
                        </div>
                        <div>
                            <img src="../../../../assets/dist/img/total-deposit.png" alt="deposit">
                        </div>
                    </div>
                    <div class="ggr-card">
                        <div class="ggr-wrap flex-wrap">
                            <div class="ggr-head">
                                <h5>Total Withdraw
                                  <!-- <i data-toggle="tooltip"
                                                                title="Total Cash + Non-Cash Withdraw"
                                                                class="fas fa-info-circle c1 mx-1">
                                   </i> -->
                                </h5>
                                <!-- <p>Net Gaming Revenue</p> -->
                                <h3>
                                  <span [ngStyle]="{'color': totalWithdraw < 0 ? 'red' : ''}"
                                           [innerHTML]="((totalWithdraw || 0) | amountFormat: tenantBaseCurrency : '': true: 'h3-currency') "></span>
                               </h3>
                            </div>
                            <span class="vertical-seprator"></span>
                            <div class="sub-cash">
                              <h6><span>Total Cash:</span> 
                                  <span [ngStyle]="{'color': totalCashWithdraw < 0 ? 'red' : ''}"
                                              [innerHTML]="((totalCashWithdraw || 0) | amountFormat: tenantBaseCurrency : '': true) "></span>
                              </h6>
                              <h6><span>Total Non Cash:</span> 
                                  <span [ngStyle]="{'color': totalNonCashWithdraw < 0 ? 'red' : ''}"
                                              [innerHTML]="((totalNonCashWithdraw || 0) | amountFormat: tenantBaseCurrency : '': true) "></span>
                              </h6>
                            </div>
                        </div>
                        <div>
                            <img src="../../../../assets/dist/img/total-withdraw.png" alt="withdraw">
                        </div>
                    </div>
                    <div class="ggr-card">
                      <div class="ggr-wrap">
                          <div class="ggr-head">
                            <h5>Total Withdraw Cancel</h5>
                              <!-- <p>otal Withdraw Cancel</p> -->
                          </div>
                          <h3> <span [ngStyle]="{'color': totalWithdrawCancel < 0 ? 'red' : ''}"
                                        [innerHTML]="((totalWithdrawCancel || 0) | amountFormat: tenantBaseCurrency : '': true: 'h3-currency') "></span></h3>
                      </div>
                      <div>
                          <img src="../../../../assets/dist/img/withdrawal-cancel.png" alt="ggr">
                      </div>
                  </div>
                  </div>


                </div>
              </div>
          <div class="row px-2 mt-3">
                    <div class="table-responsive custom-table hCustom">

                      <table class="table table-bordered">
                        <thead>
                          <tr>
                            <th>#</th>
                            <th [hidden]="!selectedColumns.includes('player_id')">
                              <div>{{'player_id' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('user_name')">
                              <div>{{'user_name' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('agent_name')">
                              <div>{{'agent_name' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('transaction_timestamp')">
                              <div>
                               Transaction Timestamp <app-ordering-arrow [column]="'created_at'" [params]="params"
                                  (changeOrder)="setOrder($event)"></app-ordering-arrow>
                              </div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('requested_timestamp')">
                              <div>
                                {{'requested_timestamp' | labelName}} <app-ordering-arrow [column]="'created_at'" [params]="params"
                                  (changeOrder)="setOrder($event)"></app-ordering-arrow>
                              </div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('action_type')">
                              <div>{{'action_type' | labelName}}</div>
                            </th>
                            <th  [hidden]="!selectedColumns.includes('utr_number')" >
                              <div>{{'utr_number' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('initial_balance')">
                              <div>{{'initial_balance' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('amount')">
                              <div>
                                {{'amount' | labelName}} <app-ordering-arrow [column]="'amount'" [params]="params"
                                  (changeOrder)="setOrder($event)"></app-ordering-arrow>
                              </div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('ending_balance')">
                              <div>{{'ending_balance' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('transfer_method')">
                              <div>{{'transfer_method' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('status')">
                              <div>{{'status' | labelName}}</div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('description')">
                              <div>{{'description' | labelName}}</div>
                            </th>
                            <th class="centerText" [hidden]="!selectedColumns.includes('transaction_timestamp')">
                              <div>
                                {{'transaction_receipt' | labelName}}
                              </div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('transaction_id')">
                              <div>
                                {{'transaction_id' | labelName}} <app-ordering-arrow [column]="'transaction_id'" [params]="params"
                                  (changeOrder)="setOrder($event)"></app-ordering-arrow>
                              </div>
                            </th>
                            <th [hidden]="!selectedColumns.includes('internal_tracking_id')">
                              <div>
                                {{'internal_tracking_id' | labelName}} <app-ordering-arrow [column]="'internal_tracking_id'"
                                  [params]="params" (changeOrder)="setOrder($event)"></app-ordering-arrow>
                              </div>
                            </th>

                          </tr>
                        </thead>
                        <tbody>
                          <ng-container
                            *ngFor="let transaction of reports | paginate: { itemsPerPage: params.size, currentPage: p, totalItems: total };  let i  = index">
                            <tr>
                              <td *ngIf="transaction?.maker_data || transaction?.checker_data; else noData">
                                <i (click)="toggleDetails(i)"
                                     [class]="selectedItemIndex === i ? 'fa fa-minus-circle' : 'fa fa-plus-circle'"
                                     style="cursor: pointer;">
                                  </i></td>
                              <ng-template #noData>
                                <td>
                                  -
                                </td>
                              </ng-template>

                              <td  [hidden]="!selectedColumns.includes('player_id')"> {{ transaction?.user_id || '--' }} </td>

                              <td  [hidden]="!selectedColumns.includes('user_name')">
                                <span *ngIf="transaction.user_name">
                                  <!-- <a href="javascript:void(0);" title="Click to Search"
                                    (click)="selectAgent(transaction?.user_name)"> -->
                                    {{ transaction?.user_name || '' }}
                                  <!-- </a>  -->
                                  &nbsp;&nbsp;
                                  <span *ngIf="isBot(transaction)" class="badge bg-dark ms-2">{{'bot' | labelName}}</span>
                                </span>
                              </td>
                              <td
                                [hidden]="!selectedColumns.includes('agent_name')">
                                <span *ngIf="transaction?.agent_name">
                                  <!-- <a href="javascript:void(0);" title="Click to Search"
                                    (click)="selectAgent(transaction?.agent_name)"> -->
                                    {{ transaction?.agent_name || '' }}
                                  <!-- </a> -->
                                </span>
                              </td>
                              <td [hidden]="!selectedColumns.includes('transaction_timestamp')">
                                {{ transaction?.created_at | date: 'dd-MM-yyyy HH:mm:ss' || '--' }} </td>
                                 <td  [hidden]="!selectedColumns.includes('requested_timestamp')">
                              {{ transaction?.requested_timestamp ? (transaction.requested_timestamp | date: 'dd-MM-yyyy HH:mm:ss') : '--' }}
                            </td>
                              <td [hidden]="!selectedColumns.includes('action_type')">
                                {{ trnType(transaction?.transaction_type) || 'NA' }} </td>
                              <td
                              [hidden]="!selectedColumns.includes('utr_number')">
                                {{ transaction?.utr_number || 'NA' }} </td>
                              <td
                                [hidden]="!selectedColumns.includes('initial_balance') ">
                                <ng-container
                                  *ngIf="transaction?.initial_balance != null">
                                  <!-- <img class="custom-currency-icon"
                                    src="../../../../assets/dist/svg/{{transaction?.currency | lowercase}}_white.svg"
                                    alt=""> -->
                                  <!-- {{ ((transaction?.initial_balance || 0.0) | amountFormat: transaction?.currency : '': false) }} -->
                                  <span [innerHTML]="((transaction?.initial_balance || 0.0) | amountFormat: transaction?.currency : '')"></span>
                                </ng-container>
                              </td>

                              <td
                                [hidden]="!selectedColumns.includes('amount') ">
                                <ng-container *ngIf="transaction?.amount != null">
                                  <!-- <img class="custom-currency-icon"
                                    src="../../../../assets/dist/svg/{{transaction?.currency | lowercase}}_white.svg"
                                    alt=""> -->
                                  <!-- {{ ((transaction.amount || 0.0) | amountFormat: transaction?.currency : '': false )}}   -->
                                  <span [innerHTML]="((transaction.amount || 0.0) | amountFormat: transaction?.currency : '')"></span>
                                  &nbsp;<i *ngIf="transaction?.source_currency && transaction?.target_currency && transaction.source_currency !== transaction.target_currency" data-toggle="tooltip"
                                  data-html="true"
                                  [attr.title]="'Source Currency: ' + transaction.source_currency + '&#13;Target Currency: ' + transaction.target_currency"
                                  class="fas fa-info-circle"></i>
                                </ng-container>
                              </td>

                              <td [hidden]="!selectedColumns.includes('ending_balance') ">
                                <ng-container
                                  *ngIf="transaction?.ending_balance != null">
                                  <!-- <img class="custom-currency-icon"
                                    src="../../../../assets/dist/svg/{{transaction?.currency | lowercase}}_white.svg"
                                    alt=""> -->
                                  <!-- {{ ((transaction?.ending_balance || 0.0) | amountFormat: transaction?.currency : '': false) }} -->
                                  <span [innerHTML]="((transaction?.ending_balance || 0.0) | amountFormat: transaction?.currency : '')"></span>
                                </ng-container>
                              </td>

                              <td [hidden]="!selectedColumns.includes('transfer_method') " >
                                {{ transaction?.payment_method || '--' }} </td>
                              <td
                              [hidden]="!selectedColumns.includes('status') ">
                                {{
                                transaction?.status || '--' }} </td>
                              <td  [hidden]="!selectedColumns.includes('description')">
                                {{ (transaction?.comments || '--') | labelName: true }} </td>
                              <td [hidden]="!selectedColumns.includes('transaction_timestamp')" class="text-center txtDec">
                                <a *ngIf="(transaction?.transaction_type == 'deposit' || transaction?.transaction_type == 'non_cash_granted_by_admin')" class="view-status noText" href="javascript:void(0);" (click)="openReceiptModal(transaction)">{{'view_receipt' | labelName}}</a>
                                <p *ngIf="!(transaction?.transaction_type == 'deposit' || transaction?.transaction_type == 'non_cash_granted_by_admin')">-</p>
                              </td>
                              <td [hidden]="!selectedColumns.includes('transaction_id')">
                                {{ transaction?.transaction_id || '--' }} </td>
                              <td [hidden]="!selectedColumns.includes('internal_tracking_id')">
                                {{ transaction?.id || '--' }} </td>

                            </tr>
                            <ng-container
                              *ngIf="selectedItemIndex === i && (transaction?.maker_data || transaction?.checker_data)">
                              <tr *ngIf="transaction?.maker_data">
                                <th colspan="3"><b>{{'maker_email' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'email', 'maker') }}</td>
                              </tr>
                              <tr *ngIf="transaction?.maker_data">
                                <th colspan="3"><b>{{'maker_username' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'user_name', 'maker') }}</td>
                              </tr>
                              <tr *ngIf="transaction?.maker_data">
                                <th colspan="3"><b>{{'maker_time_stamp' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'time_stamp', 'maker') }}</td>
                              </tr>
                              <tr *ngIf="transaction?.maker_data">
                                <th colspan="3"><b>{{'maker_remark' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'remark', 'maker') }}</td>
                              </tr>

                              <tr *ngIf="transaction?.checker_data">
                                <th colspan="3"><b>{{'checker_email' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'email', 'checker') }}</td>
                              </tr>
                              <tr *ngIf="transaction?.checker_data">
                                <th colspan="3"><b>{{'checker_username' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'user_name', 'checker') }}</td>
                              </tr>
                              <tr *ngIf="transaction?.checker_data">
                                <th colspan="3"><b>{{'checker_time_stamp' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'time_stamp', 'checker') }}</td>
                              </tr>
                              <tr *ngIf="transaction?.checker_data">
                                <th colspan="3"><b>{{'checker_remark' | labelName}}</b></th>
                                <td colspan="2">{{ getMakerCheckerData(transaction, 'remark', 'checker') }}</td>
                              </tr>
                            </ng-container>
                          </ng-container>
                          <tr *ngIf="firstTimeApiCall && reports && reports.length === 0">
                            <td colspan="16" class="tnodata"> {{'no_records_found' | labelName}} </td>
                          </tr>
                          <tr *ngIf="!firstTimeApiCall">
                            <td colspan="16" class="tnodata"> {{'click_search_to_load_data' | labelName}} </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <!--<pagination-controls (pageChange)="pageChanged($event)" (pageChange)="p = $event"></pagination-controls>-->
                      <div class="custom-pagination"  *ngIf="total>0" >
                        <div class="pagination-item-list">
                          <pagination-controls (pageChange)="pageChanged($event)"
                            (pageChange)="p = $event" [nextLabel]=this.permissionService.PAGINATION.NEXT [previousLabel]=this.permissionService.PAGINATION.PREVIOUS [maxSize] = this.permissionService.PAGINATION.MAX_SIZE></pagination-controls>
                          <select class="form-control" id="size" (change)="submitFilter()" [(ngModel)]="params.size">
                            <option *ngFor="let pageSize of pageSizes" [value]="pageSize.name">{{pageSize.name}}
                            </option>
                          </select>
                        </div>

                        <div class="totalRecords"><span>Showing {{pageCurrentTotal}} to {{ pageNextTotal }} of {{total}}
                            entries</span>
                        </div>
                      </div>
                      <!--Total Record-{{total}}-->

                      </div>
                      </div>
                  </div>
                  <!-- /.card -->
              </div>
          </div>
      </div>
  </section>
</div>

  <ng-template #loader>

    <div class="w-full text-center custom-loader-div d-flex justify-content-center mt-4">
      <!-- <img class="chart-loader" src="./assets/dist/gif/loader.gif" /> -->
      <div class="custom-loader"></div>
    </div>

  </ng-template>

  <div class="modal fade custom-modal" id="receipt-modal" aria-hidden="true" style="display: none">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">{{'view_receipt' | labelName}}</h4>
          <button type="button" class="close edit-btn-grp" data-dismiss="modal" aria-label="Close" (click)="closeReceiptModal()">
            <i class="fa fa-times" aria-hidden="true"></i>
          </button>
        </div>

        <div class="modal-body" >
            <img class="p-1" style="height:400px; object-fit:contain;" width="100%" [src]="this.selectedTransactionReceipt" alt="Card image cap">
        </div>
        <div class="modal-footer">
          <a href="{{ this.selectedTransactionReceipt ||'NA' }}" download="download" class="btn edit-btn-grp mx-auto"><i class="fa fa-download"></i></a>
        </div>

      </div>
    </div>
  </div>










<!-- ALL FILTERS USED IN DASHBOARD -->
<ng-template #reusableFilterBlock let-filterKey="filterKey" let-mainFilter="mainFilter">

  <ng-container *ngFor="let item of customFilters && customFilters[filterKey] ? customFilters[filterKey] : []">
    <!-- <div class="form-group"  [ngClass]="{'col-md': mainFilter}"> -->
      <switch [ngSwitch]="item" [ngClass]="{ 'col-md ': mainFilter }" >


        <ng-container *ngSwitchCase="'userId'">
          <!-- this.permissionService.checkPermission('players','R') -->
          <div class="form-group" class="form-group" [ngClass]="{
            'select-form': initialFilters[item] != params[item]
         }">
            <label for="user">{{'user' | labelName}}</label>
            <select class="form-control" [attr.data-placeholder]="'select' | labelName" search_select2 [url]="searchUserUrl"  [WithData]="WithData"
              id="user" (onSelect)="selectUser($event)"></select>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="'internalTrackingId'">
          <div class="form-group" [ngClass]="{
            'select-form': initialFilters[item] != params[item]
         }">
            <label for="search">{{'internal_tracking_id' | labelName}}</label>
            <input type="number"
              class="form-control"
              id="search"
              (keyup)="filter($event)"
              (input)="onChangeInput($event)"
              (keydown)="preventInvalidKeys($event)"
              [(ngModel)]="params.internalTrackingId"
              [placeholder]="'Search'"
              title="Search"
              />
          </div>
        </ng-container>
        <!-- ((superAdminAuthService.superAdminTokenValue && superAdminAuthService.superAdminTokenValue.length > 0) || isUtrNumberEnable) && !isAgent -->
        <ng-container *ngSwitchCase="'utr_number'">
          <div class="form-group"  [ngClass]="{
            'select-form': initialFilters[item] != params[item]
         }">
            <label for="utr_number">{{'utr_number' | labelName}} &nbsp;<i data-toggle="tooltip"
              [title]="getUTRTooltipDescription('UTRNumber')"
              class="fas fa-info-circle"></i></label>
            <input type="text" class="form-control" id="utr_number"
              [(ngModel)]="params.utr_number" [placeholder]="'search' | labelName" />
          </div>
        </ng-container>
        <!-- false -->
        <ng-container *ngSwitchCase="'isDirectPlayer'">
          <div class="form-group"  [ngClass]="{
            'select-form': initialFilters[item] != params[item]
         }">
            <label for="isDirectPlayer">{{'all_organization_players' | labelName}}</label>
            <select class="form-control" id="isDirectPlayer" (change)="filter($event)"
              title="All Organization Players" [(ngModel)]="params.isDirectPlayer">
              <option value="all">{{'all_organization_players' | labelName}}</option>
              <option value="direct">{{'only_direct_players' | labelName}}</option>
            </select>
          </div>
        </ng-container>
<!-- this.permissionService.PERMISSIONS?.players_key?.agent_details && this.permissionService.PERMISSIONS?.agents?.R  && hideShow-->
        <ng-container *ngSwitchCase="'agent_id'">
          <div class="form-group" [ngClass]="{
            'select-form': initialFilters[item] != params[item]
         }">
            <label for="agent_id">{{'agent' | labelName}}</label>
            <select class="form-control" select2 id="agent_id" title="Select"
              (onSelect)="filterSelectAgent($event)" [(ngModel)]="params.agent_id"
              [attr.data-placeholder]="'select' | labelName">
              <!-- <option value="">{{'select_agent' | labelName}}</option> -->
              <option *ngFor="let adminUser of userList" [value]="adminUser.id">{{adminUser.first_name}}
                {{adminUser.last_name}}</option>
            </select>
          </div>
        </ng-container>


        <ng-container *ngSwitchCase="'currency'">
          <div class="form-group"  [ngClass]="{
            'select-form': initialFilters[item] != params[item]
         }">
            <label for="currency">{{'currency' | labelName}} &nbsp;<i data-toggle="tooltip"
              [title]="getUTRTooltipDescription('CurrencyFilter')"
              class="fas fa-info-circle"></i></label>
            <select class="form-control" title="Select" [attr.data-placeholder]="'select' | labelName"
              id="currency" (change)="filter($event)" [(ngModel)]="params.currency">
              <option value="">{{'select' | labelName}}</option>
              <option *ngFor="let currency of currencies" [value]="currency.code"> {{ currency.code }} </option>
            </select>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="'action_type'">
          <div class="form-group multiple-selection-group" [ngClass]="{
            'select-form': isDeepMatchValues(initialFilters[item], params[item])
         }">
            <label for="action_type">{{'select_action_type' | labelName}}</label>
            <select class="form-control" id="action_type" [attr.data-placeholder]="'select' | labelName"
            (onSelect)="selectActionType($event)" select2 multiple>
              <!-- <option value="">{{'select' | labelName}}</option> -->
              <option value="deposit">{{'deposit' | labelName}}</option>
              <!-- <option value="deposit_bonus_claim">{{'deposit_bonus_claim' | labelName}}</option> -->
              <!-- <option value="referral_bonus_claim">{{'referral_bonus_claim' | labelName}}</option> -->
              <option value="withdraw">{{'withdraw' | labelName}}</option>
              <option value="withdraw_cancel">{{'withdraw_cancel' | labelName}}</option>
              <!-- To do later as per client requirement -->
              <!-- <option value="withdraw_rejected_by_admin">Withdraw rejected by admin/provider</option> -->
              <option value="deposit_cash_admin">{{'deposit_by_admin_in_cash' | labelName}}</option>
              <option value="deposit_non_cash_admin">{{'deposit_by_admin_in_non_cash' | labelName}}</option>
              <option value="deposit_manual_user">{{'deposit_by_user_manual_in_cash' | labelName}}</option>
              <option value="deposit_gateway_user">{{'deposit_by_user_gateway_in_cash' | labelName}}</option>
              <option value="withdraw_cash_admin">{{'withdraw_by_admin_in_cash' | labelName}}</option>
              <option value="withdraw_non_cash_admin">{{'withdraw_by_admin_in_non_cash' | labelName}}</option>
              <option value="withdraw_cash_manual_user">{{'withdraw_by_user_manual_in_cash' | labelName}}</option>
              <option value="withdraw_gateway_user">{{'withdraw_by_user_gateway_in_cash' | labelName}}</option>
              <!-- <option *ngIf="isPlayerCategorisationEnable || (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0)" value="player_bulk_categorization_bonus">{{'loyalty_bonus_claimed' | labelName}}</option> -->
              <!-- <option *ngIf="(affiliatedSmartico && this.permissionService.checkPermission('affiliated_system_smartico', 'R'))" value="royalty_non_cash_bonus">{{'loyalty_non_cash_bonus' | labelName}}</option>
              <option *ngIf="(affiliatedSmartico && this.permissionService.checkPermission('affiliated_system_smartico', 'R'))" value="royalty_cash_bonus">{{'loyalty_cash_bonus' | labelName}}</option> -->
              <option *ngIf="(oneTimeBonus && this.permissionService.PERMISSIONS?.one_time_bonus_deposit?.R)" value="one_time_bonus_deposit">{{'free_bets_deposit' | labelName}}</option>
              <option *ngIf="(oneTimeBonus && this.permissionService.PERMISSIONS?.one_time_bonus_withdrawal?.R)" value="one_time_bonus_withdraw">{{'free_bets_withdraw' | labelName}}</option>
              <option *ngIf="hasSportsFreeBetDepositAndWithdrawPermission()" value="sports_free_bet_deposit">Sports Free Bet Deposit
              </option>
              <option *ngIf="hasSportsFreeBetDepositAndWithdrawPermission()" value="sports_free_bet_withdraw">Sports Free Bet Withdraw
              </option>
              <!-- <option *ngIf="(this.permissionService.checkPermission('one_time_bonus','R'))" value="free_bets_on_deposit_bonus_claim">Free Bets On Deposit Bonus Claim</option>
              <option *ngIf="(this.permissionService.checkPermission('sports_free_bet','R'))" value="sports_free_bets_deposit_bonus_claim">Sports Free Bets Deposit Bonus Claim</option> -->
            </select>
          </div>
        </ng-container>

        <ng-container  *ngSwitchCase="'time_type'">
          <div class="form-group"  [ngClass]="{
            'select-form': isDeepMatchValues(initialFilters[item], params[item])
         }">
            <label for="time_date_period">{{'DATE_RANGE' | labelName}}</label>
            <select class="form-control" id="time_date_period" (change)="filter($event,true);disbleSearch()"
              [(ngModel)]="params.time_type">
              <option disabled value="">{{'select' | labelName}}</option>
              <option value="today">{{'today' | labelName}}</option>
              <option value="yesterday">{{'yesterday' | labelName}}</option>
              <option value="weekly">{{'this_week' | labelName}}</option>
              <option value="monthly">{{'this_month' | labelName}}</option>
              <!-- <option value="yearly">{{'this_year' | labelName}}</option> -->
              <option value="custom">{{'custom_date' | labelName}}</option>
            </select>
          </div>
        </ng-container>

        <!--params.time_type=='custom' -->
        <ng-container  *ngSwitchCase="'time_period'">
          <div class="form-group"  [ngClass]="{
            'select-form': isDeepMatchValues(initialFilters[item], params[item])
         }">
            <label for="time_period">{{'DATE_RANGE' | labelName}}</label>
            <input autocomplete="off" readonly data-placeholder="Search Time Period"
              placeholder="Search Time Period" title="Search Time Period" type="text"
              class="form-control float-right" id="time_period" [format]="format" daterangetime_picker
              [maxDate]="maxDate" (onSelect)="selectDateRange($event);updateSearch()" />
          </div>
        </ng-container>

        <!-- (isSuperAdmin &&secondBackOfficeDomainExistsForTenant == true) || (this.permissionService.checkBotDomain() == true) -->
        <ng-container  *ngSwitchCase="'playerCategory'">
          <div class="form-group" [ngClass]="{
            'select-form': initialFilters[item] != params[item]
         }">
            <label for="sb_player_type">{{'player_type' | labelName}}</label>
            <select class="form-control" id="sb_player_type" (change)="onPlayerTypeChange($event)" [(ngModel)]="params.playerCategory">
               <option value="" selected disabled>{{'select' | labelName}}</option>
              <option value="all_players">{{'all_players' | labelName}}</option>
              <option value="real_players">{{'real_players' | labelName}}</option>
              <option value="bot_players">{{'bot_players' | labelName}}</option>
            </select>
          </div>
        </ng-container>

        <ng-container *ngSwitchCase="'additional_filter'">

          <div class=" moreBtnGroup" *ngIf="filterGroups && filterGroups.length > 1">
            <button type="button" class="btn moreFilter" (click)="toggleFilterModal()">
              <p>{{'more_filters' | labelName}} <span *ngIf="filterCount != 0" class="filterCount"> {{filterCount}} </span></p>
              <img src="../../../../assets/dist/svg/filterIcon.svg" alt="filter">
            </button>
          </div>
        </ng-container>


        <ng-container *ngSwitchDefault>
        </ng-container>
      </switch>
    <!-- </div> -->

  </ng-container>

</ng-template>

<!-- filtersModal -->
<filter-modal [show]="showFilterModal" (close)="toggleFilterModal()" (apply)="applyFilters()"
title="Player Financial Report"
  (reset)="resetFilter()">
  <ng-container *ngFor="let filter of filterGroups">
    <ng-container *ngIf="customFilters && customFilters[filter] && customFilters[filter].length > 0 && filter !== 'MAIN_FILTERS' && filter !== 'BUTTON'">
      <div class="innerFilterBodyWrap">
        <h6>{{filter | labelName }}</h6>
        <div class="innerFilterWrap">
          <ng-container
            *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: false }"></ng-container>
        </div>
      </div>
    </ng-container>
  </ng-container>
</filter-modal>

<ng-template #filtersSearch>
  <button type="button" class="btn custom-secondry-btn" (click)="resetFilter()">
    <i class="fa fa-refresh"></i> {{'reset_filter' | labelName}}
  </button>
  <button [disabled]="isLoader || disableRequest" type="button" class="btn custom-secondry-btn searchBtn fnt-12" (click)="submitFilter()">
    <i class="fa fa-search"></i> {{'search' | labelName}}
  </button>
</ng-template>
