import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import * as moment from 'moment-timezone';
import { Currency, Role } from 'src/app/models';
import { PermissionService } from 'src/app/services/permission.service';
import { Constants, PageSizes, TIMEZONE, TenantConfigDescription, convertKeysToCamelCase, getTransactionType, isDateRangeWithinDuration } from 'src/app/shared/constants';
import { CustomValidators } from 'src/app/shared/validators';
import { AdminAuthService } from '../../admin/services/admin-auth.service';
import { SuperAdminAuthService } from '../../super-admin/services/super-admin-auth.service';
import { TenantService } from '../../tenant/tenant.service';
import { ReportService } from '../reports.service';
import { ActivatedRoute, Router } from '@angular/router';
import { domainDetails, generateApiUrl, getDomainNameFromHost, getSuperTenantsList, isDeepMatch, validateDateRange } from 'src/app/services/utils.service';
import { FilterConfig, FilterService } from 'src/app/services/filter.service';
import { BaseFilterComponent } from 'src/app/shared/components/base-filter/base-filter.component';
import { Select2ResetService } from 'src/app/services/select2-reset.service';

declare const $: any;
declare const toastr: any;
@Component({
  selector: 'app-player-financial-db-report',
  templateUrl: './player-financial-db-report.component.html',
  styleUrls: ['./player-financial-db-report.component.scss' , '../report.component.scss']
})
export class PlayerFinancialDbReportComponent extends BaseFilterComponent implements OnInit, OnDestroy {
  isSubAgentModuleAllowed:boolean = true;
  isPrimaryCurrencyDataEnable:boolean = true;

  maxDate: any = new Date().setHours(23,59,59,59);
  minDate:any = moment().subtract(180, 'days').startOf('day');
  selectedItemIndex: number | null = null;
    roles: string = localStorage.getItem('roles') || '';
    isAgent:boolean = false;
    reports: any[] = [];
    userList: any[] = [];
    tenantsOwnerList: any[] = [];
    tenantsList: any[] = [];
    currenciesResultArray: any[] = [];
    p: number = 1;
    currencies: Currency[] = [];
    format: string = "YYYY-MM-DD HH:mm:ss";
    totalbetcount: number = 0;
    total: number = 0;
    pageNextTotal: number = 0;
    pageCurrentTotal: number = 0;
    totalDeposit: number = 0;
    totalWithdraw: number = 0;
    totalWithdrawCancel: number = 0.00;

    allColumns:any[]=[];

    selectedColumns:any[]=[
    "all",
    "player_id",
    "user_name",
    "agent_name",
    "transaction_timestamp",
    "action_type",
    "initial_balance",
    "amount",
    "ending_balance",
    "transfer_method",
    "status",
    "description",
    "transaction_id",
    "internal_tracking_id",
    "requested_timestamp"
];
    expanded:boolean=false;

    dataTableParams: any = {
        searching: false,
        info: true,
        lengthChange: true,
        autoWidth: false,
        responsive: true,
        "dom": '<"top"lp>t<"bottom"ip><"clear">',
        lengthMenu: PageSizes.map(m => m.name),
        buttons: [{ extend: 'csv', text: 'Export as CSV', className: 'ctsmcsv mgt btn btn-info mt-0' }]
      };


    tenantBaseCurrency:any = '';
    breadcrumbs: Array<any> = [
        {title: 'Home', path: '/super-admin'},
        {title: 'Player Financial Report', path: '/reports/player-financial-report'},
    ];
    searchPlaceholder: string;
    isUtrNumberEnable:boolean = false;
    reporting_email: any;
    email_verified: boolean = false;
    selectedTransactionReceipt: any;
    imageDomain = Constants.getImageDomain;
    // isSuperAdmin is already declared in BaseFilterComponent
    searchUserUrl: string = '';
    sameAsOldParams:boolean = true
    oldParams:any = {}
    word: any
    initialFilters: any = {
      size: 25,
      page: 1,
      search: '',
      internalTrackingId: '',
      agent_id: '',
      currency: '',
      owner_id: '',
      time_zone: 'UTC +00:00',
      time_zone_name: 'UTC +00:00',
      tenant_id: '',
      action_type: [],
      utr_number: '',
      status: '',
      userId: '',
      time_period: {},
      datetime: {},
      isDirectPlayer: 'all',
      time_type: 'today',
      order: 'desc',
      sort_by: 'user_id',
      playerCategory : (this.permissionService.checkBotDomain() == false) ? 'all_players' : 'real_players',

    };

    params: any = {...this.initialFilters};
  totalCashDeposit: any;
  totalNonDeposit: any;
  totalCashWithdraw: any;
  totalNonCashWithdraw: any;

    // Properties already defined in BaseFilterComponent are not redeclared:
    // - customFilters, filterGroups, showFilterModal, filterCount

    constructor(private ReportService: ReportService,
                public adminAuthService: AdminAuthService,
        public superAdminAuthService: SuperAdminAuthService,
        private tenantService: TenantService,
        public router:Router,
        public route:ActivatedRoute,
        public select2ResetService: Select2ResetService,
        public override permissionService: PermissionService,
        public override filterService: FilterService
      ) {
        super(filterService, permissionService, adminAuthService, superAdminAuthService);
            let permissions = {
                // Permissions for each field
                // "Player ID": null,
                // "Player Name": "user_name",
                // "Phone": "phone",
                "Txn ID": null
            };

              this.searchPlaceholder = this.modifyKeysBasedOnConditions(permissions).join(' / ')

        this.params = { ...this.params, ...this.ReportService.playerFinanceReportParams };
        this.isSubAgentModuleAllowed = (!!this.permissionService.MODULES?.subAgent ? true : false);
        this.isPrimaryCurrencyDataEnable = (!!this.permissionService.MODULES?.primaryCurrencyDataEnable ? true : false);
        this.isPlayerCategorisationEnable = (!!this.permissionService.MODULES?.playerCategorization ? true : false);
        this.oneTimeBonus = (!!this.permissionService.MODULES?.oneTimeBonus ? true : false);
        this.affiliatedSmartico = (!!this.permissionService.MODULES?.affiliatedSystemSmartico ? true : false);

        const userData = JSON.parse(localStorage.getItem('userData') || '{}');

        this.reporting_email = userData ? userData.reporting_email : null;
       this.email_verified = userData ? userData.reporting_email_verified : null;

    }

    override ngOnInit(): void {
      super.ngOnInit();
        this.isUtrNumberEnable = (!!this.permissionService.MODULES?.utrNumber ? true : false);

      if (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {
        this.getTenantList();
        this.hideShow = false;
      } else {
      // this.getReport();
        this.getCurrencyList();
        this.getAgentUserList();
      }
      this.resetFilter(true);
      // Initialize filters
      this.getAllFilters();
      this.columnPermission();
        this.searchUserUrl = generateApiUrl() + `admin/transactions/users/new2?owner_type=User`;
        if (this.adminAuthService.adminTokenValue && this.adminAuthService.adminTokenValue.length > 0) {

          if(this.roles) {
            const roles = JSON.parse(this.roles);
            if(roles && roles.findIndex( (role: any) => role === Role.Admin ) == -1 && roles.findIndex( (role: any) => role === Role.Agent ) > -1) {
              this.isAgent = true;
               this.getAllFilters();
              this.columnPermission();
            }

          }

        }
        this.pageNextTotal = this.params.size;
        this.pageCurrentTotal = this.p;



    }
    ngAfterViewInit(): void {
      $('#time_period').val('today');
      $('#owner_id option:eq(0)').prop('selected',true);
      setTimeout(() => {
        $('#sb_player_type').val(this.params.playerCategory).trigger('change');
      },10)
        setTimeout(() => {
            // $('#time_period').val('today');
            $('#player_type').val('real_players').trigger('change');
        }, 100);
  }
    getTenantOwnerList(id: number) {

        if (this.superAdminAuthService.superAdminTokenValue &&
            this.superAdminAuthService.superAdminTokenValue.length > 0) {
            this.ReportService.getSuperTenantsOwnerList({id}).subscribe((res: any) => {
                this.tenantsOwnerList = res.record;
                this.params.owner_id = this.tenantsOwnerList.length > 0 ? this.tenantsOwnerList[0].id : '';
                // this.getReport();
                this.disableRequest = false;
            });
        }else{
          this.disableRequest = false;
        }

        this.getAllFilters()
    }


    getTenantList() {

        if (this.superAdminAuthService.superAdminTokenValue &&
            this.superAdminAuthService.superAdminTokenValue.length > 0) {

              this.tenantsList = getSuperTenantsList();
              if (this.tenantsList.length > 0) {
                  this.params.tenant_id = 0;
                  this.getTenantOwnerList(0);
                  this.getCurrencyList('0');
                  this.checkSecondBackOfficeDomainExists(this.params.tenant_id);
              }
        }
    }

    trnType(value:any){
      return getTransactionType(value)
    }

    selectAgent(searchValue: any) {

        // this.p = 1;
        this.params = {...this.params, search:searchValue};

        if (searchValue) {

            setTimeout(() => {
                $('#search').val(searchValue).trigger('keyup');
            }, 0);
            // this.getReport();
        }
    }



    selectDateRange(time_period: any) {
        this.params = {...this.params, time_period}
        // Call getAllFilters to update filters when time_period changes
        this.getAllFilters();
        // this.getReport();
    }
    convertToUTC(inputDate: string, inputTimezone: string): string {
        const date = moment.tz(inputDate, inputTimezone);
        const utcDate = date.utc().format('YYYY-MM-DD HH:mm:ss');
        return utcDate;
      }
    getReport() {
    super.applyFilters(this.params, this.initialFilters, () => {});

      let { hasAccess, noAccessTenantIds } = this.permissionService.managerAllPermissionAvailable(this.params.tenant_id, 'player_financial_report','R');
      if(!hasAccess){
        let tenantNames = this.tenantsList.filter((tenant: any) => noAccessTenantIds.includes(tenant.id)).map((tenant: any) => tenant.name).join(', ');
        toastr.error(`You don't have access for ${tenantNames}`);
        return;
      }
      this.filterService.updateTimezone(this.params, this.adminAuthService);
      let time_period = this.params.time_period
      let time_type = this.params.time_type
      if (time_type == 'custom' && !isDateRangeWithinDuration(time_period)) {
        toastr.error('Custom Date should be in range of 90 days');
        return;
      }
      this.isLoader = true;
      this.firstTimeApiCall = true;
      this.filterService.updateTimezone(this.params, this.adminAuthService);

      $('.ctsmcsv').remove();
      if(this.params.time_period?.start_date && this.params.time_period?.end_date && this.params.time_zone_name && this.params.time_zone_name !== 'UTC +00:00'){
        this.params = {...this.params, datetime : {
          start_date: this.convertToUTC(this.params.time_period.start_date, this.params.time_zone_name),
            end_date:this.convertToUTC(this.params.time_period.end_date, this.params.time_zone_name)
        }}
      } else{
            this.params = {...this.params, datetime : { start_date: this.params.time_period?.start_date, end_date:this.params.time_period?.end_date}}
        }

        // let {startDate, endDate} = { startDate: this.params.datetime.start_date, endDate: this.params.datetime.end_date }
        // this.params.dateTime = (this.params.datetime)? JSON.stringify({startDate, endDate}) : "{}"
        // let modifiedParams = convertKeysToCamelCase(this.params)
        let modifiedParams = this.getModifiedParams(this.params)
        modifiedParams.actionType = this.params.action_type && this.params.action_type.length > 0
        ? JSON.stringify(this.params.action_type)
        : '';

        modifiedParams.totals = false
        this.getRecords(modifiedParams)

        if (!this.sameAsOldParams) {
          this.getRecordCount(modifiedParams)
        }
        this.setPageCount(this.p);
    }

    getRecords(modifiedParams: any) {
      this.ReportService.getSuperAdminPlayerFinancialDbReport(modifiedParams).subscribe((res: any) => {
        this.reports = res?.data?.data?.result?.rows  || []
        this.tenantBaseCurrency = res?.data?.data?.result?.tenantBaseCurrency || ''
        this.isLoader = false
      },
        (error: any) => {
          this.isLoader = false
        });
    }
    getRecordCount(modifiedParams: any) {
      modifiedParams = { ...modifiedParams, count: true }
      this.ReportService.getSuperAdminPlayerFinancialDbReport(modifiedParams).subscribe((res: any) => {
        this.total = res?.data?.data?.result?.total;

        let limit = this.params.size;
        let offset = 1;
        if (this.p > 1) {
          offset = limit * (this.p - 1);
        }
        this.pageCurrentTotal = offset;

        this.pageNextTotal = (offset > 1 ? offset : 0) + parseInt(limit);
        if (this.total < this.pageNextTotal) {
          this.pageNextTotal = this.total;
        }
        this.isLoader = false
      },
        (error: any) => {
          this.isLoader = false
        });
    }
    setPageCount(page: number) {
      let limit = this.params.size;
      let offset = page;

      if (page > 1) {
        offset = limit * (page - 1);
      }
      this.pageCurrentTotal = offset;
      this.p = page;
      this.pageNextTotal = (offset > 1 ? offset : 0) + parseInt(limit);
      if (this.total < this.pageNextTotal) {
        this.pageNextTotal = this.total;
      }
    }

    getReportTotal() {

      let { hasAccess, noAccessTenantIds } = this.permissionService.managerAllPermissionAvailable(this.params.tenant_id, 'player_financial_report','R');
      if(!hasAccess){
        let tenantNames = this.tenantsList.filter((tenant: any) => noAccessTenantIds.includes(tenant.id)).map((tenant: any) => tenant.name).join(', ');
        toastr.error(`You don't have access for ${tenantNames}`);
        return;
      }
      let time_period = this.params.time_period
      let time_type = this.params.time_type
      if (time_type == 'custom' && !isDateRangeWithinDuration(time_period)) {
        // toastr.error('Custom Date should be in range of 90 days');
        return;
      }
      this.isLoader = true;
      this.firstTimeApiCall = true;
      this.filterService.updateTimezone(this.params, this.adminAuthService);

      $('.ctsmcsv').remove();
      if(this.params.time_period?.start_date && this.params.time_period?.end_date && this.params.time_zone_name && this.params.time_zone_name !== 'UTC +00:00'){
        this.params = {...this.params, datetime : {
          start_date: this.convertToUTC(this.params.time_period.start_date, this.params.time_zone_name),
            end_date:this.convertToUTC(this.params.time_period.end_date, this.params.time_zone_name)
        }}
      } else{
            this.params = {...this.params, datetime : { start_date: this.params.time_period?.start_date, end_date:this.params.time_period?.end_date}}
        }

        let modifiedParams = this.getModifiedParams(this.params)
        modifiedParams.actionType = this.params.action_type && this.params.action_type.length > 0
        ? JSON.stringify(this.params.action_type)
        : '';
        modifiedParams.totals = true

            this.ReportService.getSuperAdminPlayerFinancialDbReport(modifiedParams).subscribe((res: any) => {
                this.getPrepareRecordTotal(res);
            }, (error:any) => {
        this.isLoader = false

      });
    }
  getStartEndDate(input: any) {
    let response: any = '{}'
    if ((input.start_date || input.startDate) && (input.end_date || input.endDate)) {
      response = { startDate: (input.start_date || input.startDate), endDate: (input.end_date || input.endDate) }
    }
    return response;
  }
  getModifiedParams(params: any) {
    let modifiedParams = convertKeysToCamelCase(params)

    modifiedParams.dateTime = "{}"
    if (modifiedParams.timeType == 'custom') {
      modifiedParams.dateTime = (modifiedParams.datetime) ? JSON.stringify(this.getStartEndDate(modifiedParams.datetime)) : "{}"
      modifiedParams.timePeriod = (modifiedParams?.timePeriod) ? JSON.stringify(this.getStartEndDate(modifiedParams.timePeriod)) : "{}"
    }
    if (!modifiedParams.timePeriod || !modifiedParams?.timePeriod?.length) {
      modifiedParams.timePeriod = "{}"
    }

    if (modifiedParams.actionType || modifiedParams?.actionType?.length) {
      modifiedParams.actionType = (modifiedParams?.actionType || modifiedParams?.actionType?.length) ? JSON.stringify(modifiedParams.actionType) : modifiedParams.actionType
    }
    delete modifiedParams.datetime
    let modifiedParamswithoutPage = { ...modifiedParams }
    delete modifiedParamswithoutPage.page
    delete modifiedParamswithoutPage.size
    delete modifiedParamswithoutPage.totals
    delete modifiedParamswithoutPage.send_email

    this.sameAsOldParams = isDeepMatch(this.oldParams, modifiedParamswithoutPage)
    this.oldParams = { ...modifiedParams }
    delete this.oldParams.page
    delete this.oldParams.size
    delete this.oldParams.totals
    delete this.oldParams.send_email
    return modifiedParams
  }
    getPrepareRecord(res: any) {

        this.reports = res?.data?.data?.result?.rows || []
        this.tenantBaseCurrency = res?.data?.data?.result?.tenantBaseCurrency || ''
        // this.total = res?.data?.data?.result?.total || 0

        // let limit = this.params.size;
        // let offset = 1;
        // if (this.p > 1) {
        //     offset = limit * (this.p - 1);
        // }
        // this.pageCurrentTotal = offset;

        // this.pageNextTotal = (offset>1?offset:0) + parseInt(limit);
        // if(this.total < this.pageNextTotal){
        //     this.pageNextTotal = this.total;
        // }
        this.isLoader = false
    }

    getPrepareRecordTotal(res: any) {
      this.totalDeposit = res?.data?.data?.result?.rows?.totalAmount?.total_deposit?.value
      this.totalWithdraw = res?.data?.data?.result?.rows?.totalAmount?.total_withdraw?.value
      this.totalCashDeposit = res?.data?.data?.result?.rows?.totalAmount?.total_cash_deposit?.value
      this.totalNonDeposit = res?.data?.data?.result?.rows?.totalAmount?.total_non_deposit?.value
      this.totalCashWithdraw = res?.data?.data?.result?.rows?.totalAmount?.total_cash_withdraw?.value
      this.totalNonCashWithdraw = res?.data?.data?.result?.rows?.totalAmount?.total_non_cash_withdraw?.value
      this.totalWithdrawCancel = res?.data?.data?.result?.rows?.totalAmount?.total_withdraw_cancel?.value
      this.isLoader = false
    }

    getAll(){
      this.getReport()
    }
    filterSelectOwner(evt: any) {
        // this.p = 1;
        this.params = {...this.params, owner_id: evt};
        this.getAgentUserList();
        // Call getAllFilters to update filters when owner_id changes
        this.getAllFilters();
        // this.getReport();
    }

    getAgentUserList(tenantid='') {

        if (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {

            if(!tenantid){
                tenantid=this.params.tenant_id;
            }
            this.ReportService.getSuperAdminUserList({"owner_id":this.params.owner_id,"tenant_id":tenantid}).subscribe((res: any) => {
                this.userList = res.record;
            });
        } else {
            this.ReportService.getAdminUserList().subscribe((res: any) => {
                this.userList = res.record;
            });
        }

    }

    filterSelectAgent(evt: any) {
    if(evt == null) return;
        // this.p = 1;
        this.params = {...this.params, agent_id: evt};
        // Call getAllFilters to update filters when agent_id changes
        this.getAllFilters();
        // if(evt)
        // this.getReport();
    }

    filter(evt: any, dateFilterType: boolean=false) {
        // Update params based on the event if needed
        if (evt && evt.target && evt.target.id) {
            const id = evt.target.id;
            if (id === 'currency') {
                this.params.currency = evt.target.value;
            } else if (id === 'isDirectPlayer') {
                this.params.isDirectPlayer = evt.target.value;
            }
        }

        if(this.params.time_type=='custom'){
            this.customDate=true;
            if(dateFilterType){
                setTimeout(() => {
                  $('#time_period').val('');
                }, 10);
              }
            // if(!dateFilterType)
            // this.getReport();
        }else{
            this.customDate=false;
        }
        // Call getAllFilters to update filters when any filter changes
        this.getAllFilters();
    }

    submitFilter(){
        this.p = 1;
        this.params = { ...this.params, page: this.p };
        if(this.disableRequest){ return }
        this.getReport();
        if (!this.sameAsOldParams) {
          this.getReportTotal()
        }
        this.setPageCount(this.p);
    }

    pageChanged(page: number) {
      this.params = {...this.params, page};
      if(this.disableRequest){ return }
      this.getReport();
      if (!this.sameAsOldParams) {
        this.getReportTotal()
      }
      this.setPageCount(page);
    }

  resetFilter(nodata = false) {
    this.p = 1;
    this.params = { ...this.initialFilters };
    setTimeout(() => {
      $("#agent_id").val('').trigger('change');
      $("#tenant_id").val(0).trigger('change');
      $('#player_type').val('real_players').trigger('change');
      $('#user').val('').trigger('change');
      $('#action_type').val([]).trigger('change'); // Reset multi-select action_type
    }, 10)
    // $( "#time_zone" ).val('UTC +00:00').trigger('change');

    if (!nodata) {
      this.getReport();
      if (!this.sameAsOldParams) {
        this.getReportTotal()
      }
      this.setPageCount(this.p);
    }
    this.disableRequest = false;
    this.getAllFilters();
    super.applyFilters(this.params, this.initialFilters, () => { });
  }




    tenantFilter(evnt: any) {
      this.disableRequest=true;
        // this.p = 1;
        // this.params.page = this.p;
        this.params.tenant_id = parseInt(evnt);
       // this.params.playerCategory = 'real_players';
        this.checkSecondBackOfficeDomainExists(this.params.tenant_id)
        this.getTenantOwnerList(evnt);
        this.getCurrencyList(evnt);
        // this.getAgentUserList(evnt);
        if(evnt != 0){
            this.hideShow = true;
            this.getAgentUserList();
          }
          else{
            this.hideShow = false;
            this.userList = [];
          }
          this.getFilters();
    }

    getCurrencyList(evnt = '') {
        if (this.superAdminAuthService.superAdminTokenValue &&
            this.superAdminAuthService.superAdminTokenValue.length > 0) {

            this.ReportService.getCurrencyList(evnt).subscribe((res: any) => {
                this.currencies = res.record;
            });

        } else {

            this.ReportService.getCurrencyList().subscribe((res: any) => {
                this.currencies = res.record;
            });
        }
    }

    download(action: 'download' | 'email'){
      let module = (action === 'download') ? 'export' : 'send_email';

      let { hasAccess, noAccessTenantIds } = this.permissionService.managerAllPermissionAvailable(this.params.tenant_id, 'player_financial_report', module);
      if (!hasAccess) {
        let tenantNames = this.tenantsList.filter((tenant: any) => noAccessTenantIds.includes(tenant.id)).map((tenant: any) => tenant.name).join(', ');
        toastr.error(`You don't have access for ${tenantNames}`);
        return;
      }
      this.filterService.updateTimezone(this.params, this.adminAuthService);
      let modifiedParams = this.getModifiedParams(this.params)
      if (action === 'email') {
        modifiedParams = { ...modifiedParams, send_email: true };
      } else {
        modifiedParams = { ...modifiedParams, send_email: false };
      }
        modifiedParams.actionType = this.params.action_type && this.params.action_type.length > 0
        ? JSON.stringify(this.params.action_type)
        : '';

        let data ={
            payload : JSON.stringify(modifiedParams),
            module : 'player_financial_report',
            type : 'player_financial_report_db'
          }
          this.ReportService.export(data).subscribe(
            (res: any) => {
              toastr.success(res.message || 'CSV generation has been successfully started... You can download the file in the export section.')
            });

    }

  setOrder(sort: any) {
    this.p = 1;
    this.params = { ...this.params, page: this.p, order: sort.order, sort_by: sort.column };
    if(this.disableRequest){ return }
    this.getReport();
    if (!this.sameAsOldParams) {
      this.getReportTotal()
    }
  }

    ngOnDestroy(): void {
        this.ReportService.playerFinanceReportParams = { ...this.ReportService.playerFinanceReportParams, ...this.params };
    }
    disbleSearch(){
        this.params.time_period=[];
        if(this.params.time_type=='custom' && this.params.time_period.length==0){
          this.disableRequest=true;
        }else{
          this.disableRequest=false;

        }
      }
      updateSearch(){
          this.disableRequest=false;
      }

    modifyKeysBasedOnConditions(attributePermissions: any) {
        for (let attribute in attributePermissions) {
            if (attributePermissions.hasOwnProperty(attribute)) {
                let permissionKey = attributePermissions[attribute];
                if (permissionKey !== null) {
                    if (!this.permissionService.checkPermissionForPlayerKey('players_key', permissionKey)) {
                        delete attributePermissions[attribute];
                    }
                }
            }
        }


        let finalAttributes = Object.keys(attributePermissions);

        return finalAttributes;
    }

    toggleDetails(index: number) {
        this.selectedItemIndex = (this.selectedItemIndex === index) ? null : index;
    }

    getMakerCheckerData(txn: any, param: any, module: any) {
        let filteredObj = (module === 'maker') ? (txn.maker_data) : (txn.checker_data);
        let data
        switch (param) {
            case 'email':
                data = filteredObj.email
                break;
            case 'time_stamp':
                data = this.getFormattedDateTime(filteredObj.time_stamp)
                break;
            case 'user_name':
                data = (filteredObj?.user_name || '-')
                break;
            case 'remark':
                data = (filteredObj?.remark || '-')
                break;
            default: ''
                data = ''
                break;
        }
        return data
    }

  convertToTimeZone(utcDate: any, timeZone: any) {
    const options = { timeZone };
    return utcDate.toLocaleString('en-US', options);
  }

  getFormattedDateTime(date: any) {
    if(this.params.time_zone_name == 'UTC +00:00'){
      this.params.time_zone_name = 'Africa/Abidjan'
    }
    const formattedDateTime = this.convertToTimeZone(new Date(date), this.params.time_zone_name);
    return formattedDateTime;
  }
  getUTRTooltipDescription(key: string): string {
    return TenantConfigDescription[key] || '';
  }
  toggleSelection(column: string) {
    let checked = this.isSelected(column);
    if (column == 'ALL' && checked) {
      this.selectedColumns = [];
    } else if (column == 'ALL' && !checked) {
      this.selectedColumns = this.allColumns;
    } else {
      if (!checked) {
        this.selectedColumns.push(column);
        this.selectedColumns.length == (this.allColumns.length - 1) ? this.selectedColumns.push('ALL') : '';
      } else {
        this.selectedColumns.includes('ALL') ? this.selectedColumns = this.selectedColumns.filter(col => col !== 'ALL') : '';
        this.selectedColumns = this.selectedColumns.filter(col => col !== column);
      }
    }
  }

  getName(name: string): string {
    const preservedWords = ['UTR', 'ID']; // Add more abbreviations as needed

    return name
      .split(' ')
      .map((word: string) => {
        return preservedWords.includes(word.toUpperCase())
          ? word.toUpperCase()
          : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ');
  }

  isSelected(column: string): boolean {
    return this.selectedColumns.includes(column);
  }

  showCheckboxes() {
    var checkboxes = document.getElementById("checkboxes");
    if (checkboxes !== null) {
      if (!this.expanded) {
        checkboxes.style.display = "block";
        this.expanded = true;
      } else {
        checkboxes.style.display = "none";
        this.expanded = false;
      }
    }
  }

  openReceiptModal(transaction:any){
    this.selectedTransactionReceipt = transaction?.transaction_receipt ? (this.imageDomain + transaction?.transaction_receipt) : '';

    $('#receipt-modal').modal('show')
  }

  closeReceiptModal(){
    $('#receipt-modal').modal('hide')
    this.selectedTransactionReceipt = null;
  }


  columnPermission() {


    this.allColumns = [
      { name: 'all', permission: true, },
      { name: 'player_id', permission: this.permissionService.PERMISSIONS?.players_key?.user_name },
      { name: 'user_name', permission: this.permissionService.PERMISSIONS?.players_key?.user_name },
      { name: 'agent_name', permission: this.permissionService.PERMISSIONS?.players_key?.agent_details },
      {name: 'transaction_timestamp', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.transaction_timestamp},
      {name: 'requested_timestamp', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.requested_timestamp},
      {
        name: 'action_type',
        permission: this.permissionService.PERMISSIONS?.transaction_attributes?.action_type
      },
      {name: 'initial_balance', permission: this.permissionService.PERMISSIONS?.players_key?.before_balance},
      {name: 'amount', permission: this.permissionService.PERMISSIONS?.players_key?.total_balance},
      {
        name: 'ending_balance',
        permission: this.permissionService.PERMISSIONS?.players_key?.after_balance
      },
      {name: 'transfer_method', permission: this.permissionService.PERMISSIONS?.report_attributes?.transfer_method},
      {
        name: 'status',
        permission: this.permissionService.PERMISSIONS?.report_attributes?.status
      },
      {name: 'description', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.description},
      {
        name: 'transaction_id',
        permission: this.permissionService.PERMISSIONS?.transaction_attributes?.transaction_id
      },
      {name: 'internal_tracking_id', permission: this.permissionService.PERMISSIONS?.report_attributes?.internal_tracking_id},
      {
        name: 'utr_number',
        permission:(this.permissionService.isSuperAdmin || this.isUtrNumberEnable) && !this.isAgent && (this.permissionService.PERMISSIONS?.transaction_attributes?.utr_number)
      },
      {name: 'tenant_id', permission: this.permissionService.PERMISSIONS?.report_attributes?.tenant_id}

    ];
    if (this.selectedColumns.length) {
      this.allColumns.forEach(column => {
        if (column.permission) {
          column.selected = this.selectedColumns.includes(column.name)
        } else {
          column.selected = false
        }
      })
    }

    return this.allColumns

  }

  checkSecondBackOfficeDomainExists(tenantId: any) {
    // Use the provided tenantId parameter instead of this.params.tenant_id
    this.tenantService.checkSecondBackOfficeDomainExists({ tenantId: tenantId })
    .subscribe(
      (res: any) => {
       if (res && res.record.sboDomainExists == true && this.superAdminAuthService.superAdminUserValue?.role != 'manager') {
          this.secondBackOfficeDomainExistsForTenant = true;
          setTimeout(() => {
            $('#player_type').val('real_players').trigger('change');
          }, 100);
        }else{
          this.secondBackOfficeDomainExistsForTenant = false;
        }
        ($('#sb_player_type').val(this.params.playerCategory).trigger('change'));
        this.getAllFilters();
      }
    );
  }
  onPlayerTypeChange(event: any) {
    this.params.playerCategory = event.target.value;
    // Call getAllFilters to update filters when playerCategory changes
    this.getAllFilters();
  }


  selectActionType(action_type: any) {
    // Handle multi-select: action_type should be an array of selected values
    this.params = {
      ...this.params,
      action_type: Array.isArray(action_type) ? action_type : (action_type ? [action_type] : [])
    }
    // Call getAllFilters to update filters when action_type changes
    this.getAllFilters();
  }

    selectUser(data: any) {
    if(data.value == null) return;
    this.selectedSelect2UserData = data;
    const id = data.value
    this.params = {...this.params, userId: id}
    // Call getAllFilters to update filters when userId changes
    this.getAllFilters();
  }

  preventInvalidKeys(event: KeyboardEvent) {
    CustomValidators.preventInvalidNumberKeys(event);
  }

  onChangeInput(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.value === '') {
      input.value = '';
      this.params.internalTrackingId = '';
    }
    const maxLength = 12;
    if (input.value.length > maxLength) {
      input.value = input.value.slice(0, maxLength);
      this.params.internalTrackingId = +input.value;
    }
  }

  selectedColumnsList(event: any) {
    this.selectedColumns = event;
    this.allColumns = this.allColumns.map((player: any) => {
      player.selected = this.selectedColumns.includes(player.name)
      return player;
    })
  }
 /**
   * Applies the current filters to the player list and hides the filter modal.
   *
   * This method uses the base implementation from BaseFilterComponent
   * but provides a custom callback to get the report data.
   */
 applyFilters(): void {
  super.applyFilters(this.params, this.initialFilters, () => this.submitFilter());
}


/**
 * Retrieves and organizes all available filters for the player list component.
 *
 * This method defines a set of filters grouped by categories such as `MAIN_FILTERS`,
 * `ACCOUNT`, `BALANCE_RANGE`, `DATE_RANGE`, `PLAYER`, and `DOCUMENTS`. Each filter
 * includes a key, a group, and a permission condition that determines its availability.
 *
 * The filters are processed using the `permissionService.getFormattedFilters` method,
 * which formats the filters and groups them accordingly. The formatted filters and
 * groups are then assigned to `customFilters` and `filterGroups` respectively.
 *
 * @returns {void} This method does not return a value. It updates the `customFilters`
 * and `filterGroups` properties of the component.
 */
override getAllFilters(): void {
  const filters = this.getFilters();
 const result = this.filterService.getFormattedFilters(filters, this.filteredKeys);
  this.customFilters = result.customFormat;
  this.filterGroups = result.filterGroups;
  setTimeout(() => {
    this.select2ResetService.initializeMultiSelect(this.params.action_type, 'action_type');
    if (this.params.userId) {
      this.select2ResetService.setUserSelect2(this.selectedSelect2UserData, 'user');
     
    }
  }, 100); // Ensure the view updates after setting filters
}


// Using the base implementation of getFilterCount from BaseFilterComponent
// which already handles counting changed filters

// Using the base implementation of allFilters getter from BaseFilterComponent

// Using the base implementation of isDeepMatchValues from BaseFilterComponent
// which already handles deep comparison of arrays

/**
 * Checks if a filter value has changed from its initial value
 *
 * @param key - The filter key to check
 * @returns True if the filter value has changed, false otherwise
 */
isFilterChanged(key: string): boolean {
  return super.isFilterChanged(key, this.initialFilters, this.params);
}

/**
* Overrides the toggleFilterModal method from BaseFilterComponent
* to add custom behavior.
*/
override toggleFilterModal(): void {
  this.showFilterModal = !this.showFilterModal;
}

// =>Filters : Search,Select User,Round ID,Select agent

/**
 *
 * This method implements the abstract method from BaseFilterComponent.
 * It returns an array of filter configurations.
 *
 * @returns {FilterConfig[]} An array of filter configurations.
 */
override getFilters(): FilterConfig[] {
  // Reordered filters according to requirements: Search, Select User, Round ID, Select Agent
  return [
    // Top 4 filters as per requirements
    { group: 'PLAYER', key: 'userId', permission: this.permissionService.checkPermission('players','R'), filterKey: 'userId' },
    { group: 'ACTION', key: 'action_type', permission: true, filterKey: 'action_type' },
    { group: 'DATE_RANGE', key: 'time_type', permission: true, filterKey: 'time_type' },
    { group: 'DATE_RANGE', key: 'time_period', permission: this.params.time_type == 'custom', filterKey: 'time_period' },
    { group: 'PLAYER', key: 'agent_id', permission: this.permissionService.PERMISSIONS?.players_key?.agent_details && this.permissionService.PERMISSIONS?.agents?.R && this.hideShow, filterKey: 'agent_id' },

    { group: 'TRANSACTION', key: 'utr_number', permission: (this.permissionService.isSuperAdmin || this.isUtrNumberEnable) && !this.isAgent, filterKey: 'utr_number' },
    { group: 'TRANSACTION', key: 'internalTrackingId', permission: true, filterKey: 'internalTrackingId' },

    // Other filters - moved below the top 4 filters
    { group: 'PLAYER', key: 'isDirectPlayer', permission: false, filterKey: 'isDirectPlayer' },
    { group: 'PLAYER', key: 'currency', permission: true, filterKey: 'currency' },
    { group: 'PLAYER', key: 'playerCategory', permission: (this.isSuperAdmin &&this.secondBackOfficeDomainExistsForTenant == true) || (this.permissionService.checkBotDomain() == true), filterKey: 'playerCategory' },

  ];
}


  hasSportsFreeBetDepositAndWithdrawPermission(): boolean {
    return this.permissionService.PERMISSIONS?.sports_free_bet_deposit?.R && this.permissionService.PERMISSIONS?.sports_free_bet_withdrawal?.R;
  }

}
