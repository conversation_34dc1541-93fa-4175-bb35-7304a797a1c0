<div class="content-wrapper">
  <app-breadcrumb  title="Unified Transaction Report" class="custom-main-heading"
      [breadcrumbs]="breadcrumbs"></app-breadcrumb>
  <section class="content custom-content px-1 px-md-4">
      <div class="container-fluid px-0">
          <div class="row custom-row">
              <div class="col-md-12">
                  <div class="card mb-0">
                      <ng-container *ngIf="this.permissionService.isSuperAdmin">
                              <div class="card-header p-0 mb-2">
                                    <div class="row w-100 d-flex align-items-center mainHeadFilter">
                                    <div class="form-group col-12 col-sm-6 col-lg-3 col-xl-3 selectTenantCol">
                                        <label for="tenant_id">{{'select_tenant' | labelName}}</label>
                                        <select select2 id="tenant_id" style="width: 100%;" [attr.data-placeholder]="'select' | labelName" [(ngModel)]="params.tenantId"
                                        (onSelect)="tenantFilter($event)">
                                        <option [value]="0">{{'all_tenants' | labelName}}</option>
                                        <option *ngFor="let tenant of tenantsList" [value]="tenant.id">{{tenant.name}}</option>
                                    </select>
                                    </div>

                                    <div class="form-group col-12 col-sm-6 col-lg-3 col-xl-3 selectTenantCol" *ngIf="hideShow">
                                            <label for="owner_id">{{'select_owner' | labelName}}</label>
                                            <select class="select2custom" select2 id="owner_id" (onSelect)="filterSelectOwner($event)"
                                                [(ngModel)]="params.owner_id" style="width: 325px" [attr.data-placeholder]="'select' | labelName">
                                                <!-- <option value="">Select Owner</option> -->
                                                <option *ngFor="let tenants of tenantsOwnerList let i = index " [attr.selected]="!i"
                                                [value]="tenants.id">{{tenants.agent_name}}</option>
                                            </select>
                                    </div>


                                </div>

                            </div>
                        </ng-container>

                      <div class="card-body">
                          <div class="row mx-0 filterRow">
                              <div class="col-xxl-9">
                                    <div class="row" *ngIf=" filterGroups && filterGroups.length > 0 ; ">
                                        <ng-container *ngFor="let filter of filterGroups">
                                            <ng-container
                                                *ngIf="customFilters[filter]?.length  > 0 && filter === 'MAIN_FILTERS'"><ng-container
                                                    *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: true }"></ng-container>
                                            </ng-container>
                                        </ng-container>
                                    </div>
                                </div>
                        <div class="col-xxl-3 resetSearch">
                                    <div class="form-group custom-btn-secondary form-group-head m-0 text-start">
                                        <ng-container *ngTemplateOutlet="filtersSearch"></ng-container>
                                    </div>
                                </div>


                              <div class="col-12 px-0">
                                        <div class="row rowBtnWrap justify-content-end mx-0">


                                        <div class="d-flex justify-content-end align-items-center selectExport pr-0">
                                            <ng-container
                                                *ngIf="!disableRequest &&  ((this.permissionService.checkPermission('unified_transaction_report','export'))|| (this.permissionService.checkPermission('unified_transaction_report', 'send_email') && (reporting_email && email_verified))) && reports.length > 0">
                                                <div>
                                                    <ng-container
                                                        *ngIf="this.permissionService.checkPermission('unified_transaction_report','export')">
                                                        <button type="button" *ngIf="reports.length > 0"
                                                            class="mgt btn custom-secondry-btn"
                                                            (click)="download('download')"> <i
                                                                class="fa fa-download"></i> {{'export_as_csv' |
                                                            labelName}} </button>
                                                    </ng-container>
                                                </div>
                                                <div>
                                                    <ng-container
                                                        *ngIf="this.permissionService.checkPermission('unified_transaction_report', 'send_email') && (reporting_email && email_verified)">
                                                        <button type="button" *ngIf="reports.length > 0"
                                                            class="mgt btn custom-secondry-btn mr-2"
                                                            (click)="download('email')">
                                                            <i class="fa fa-envelope"></i> {{'send_email' | labelName}}
                                                        </button>
                                                    </ng-container>
                                                </div>
                                            </ng-container>
                                            <app-select-all [allColumns]="allColumns"
                                                (selectedColumnsChange)="selectedColumnsList($event)">
                                            </app-select-all>
                                        </div>
                                    </div>
                                  <ng-container *ngIf="tenantBaseCurrency != '' && reports.length > 0">
                                 <!-- <div class="inner-custom-total-grid mb-16" *ngIf="tenantBaseCurrency != ''">
                                    <div class="innerCus-col">
                                        <h5>Total Bet (In {{tenantBaseCurrency}}):</h5>
                                        <p>{{(total_bets| amountFormat:
                                            tenantBaseCurrency : '': false)}}</p>
                                    </div>
                                    <div class="innerCus-col">
                                        <h5>Total Win (In {{tenantBaseCurrency}}):</h5>
                                        <p>{{(total_wins| amountFormat:
                                            tenantBaseCurrency : '': false)}}</p>
                                    </div>
                                    <div class="innerCus-col">
                                        <h5>Total Win Refund (In {{tenantBaseCurrency}}):</h5>
                                        <p>{{(
                                            totalWinRefund|amountFormat: tenantBaseCurrency : '': false) }}</p>
                                    </div>
                                    <div class="innerCus-col">
                                        <h5>Total Deposit (In {{tenantBaseCurrency}}):</h5>
                                        <p>{{(total_deposit|amountFormat:
                                            tenantBaseCurrency : '': false)}}</p>
                                    </div>
                                    <div class="innerCus-col">
                                        <h5>Total Withdraw (In {{tenantBaseCurrency}}):</h5>
                                        <p>{{(total_withdraw|amountFormat:
                                            tenantBaseCurrency : '': false)}}</p>
                                    </div>
                                    <div class="innerCus-col">
                                        <h5>Total Bet Refund (In {{tenantBaseCurrency}}):</h5>
                                        <p>{{(
                                            totalBetRefund|amountFormat: tenantBaseCurrency : '': false) }}</p>
                                    </div>
                                    </div> -->
                                    <div class="custom-total-grid4 mt-3" *ngIf="tenantBaseCurrency != ''">
                                        <div class="ggr-card">
                                            <div class="ggr-wrap">
                                                <div class="ggr-head">
                                                    <h5>Total Deposit </h5>
                                                    <!-- <p>Total Bet</p> -->
                                                </div>
                                                    <h3>
                                                         <span [ngStyle]="{'color': total_deposit < 0 ? 'red' : ''}"
                                        [innerHTML]="((total_deposit || 0) | amountFormat: tenantBaseCurrency : '': true: 'h3-currency') "></span>
                                                    </h3>
                                            </div>
                                            <div>
                                                <img src="../../../../assets/dist/img/total-deposit.png" alt="deposit">
                                            </div>
                                        </div>
                                        <div class="ggr-card">
                                            <div class="ggr-wrap">
                                                <div class="ggr-head">
                                                    <h5>Total Withdraw </h5>
                                                    <!-- <p>
                                                        Total Win
                                                    </p> -->
                                                </div>
                                                    <h3>
                                                        <!-- {{(total_wins| amountFormat: tenantBaseCurrency : '': false)}} -->
                                                        <span [ngStyle]="{'color': total_withdraw < 0 ? 'red' : ''}" class="align-baseline"
                                                            [innerHTML]="((total_withdraw || 0) | amountFormat: tenantBaseCurrency : '': true: 'h3-currency') "></span>
                                                    </h3>
                                            </div>
                                            <div>
                                                <img src="../../../../assets/dist/img/total-withdraw.png" alt="withdraw">
                                            </div>
                                        </div>
                                        <div class="ggr-card custom-grid3">
                                             <div class="ggr-inrWrap">
                                                <h5>Total Win </h5>
                                            <p> <span [innerHTML]="(total_wins | amountFormat: tenantBaseCurrency : '': true)"></span></p>
                                            </div>
                                            <div class="ggr-inrWrap">
                                                <h5>Total Win Refund </h5>
                                                <p> <span [innerHTML]="(totalWinRefund | amountFormat: tenantBaseCurrency : '': true)"></span></p>
                                            </div>
                                           
                                            <div class="ggr-inrWrap">
                                                <h5>Total Bet </h5>
                                            <p> <span [innerHTML]="(total_bets | amountFormat: tenantBaseCurrency : '': true)"></span>  </p>
                                            </div>
                                            <div class="ggr-inrWrap">
                                                <h5>Total Bet Refund </h5>
                                                <p> <span [innerHTML]="(totalBetRefund | amountFormat: tenantBaseCurrency : '': true)"></span></p>
                                            </div>
                                        </div>
                                    </div>

                     
                                  </ng-container>


                              </div>
                          </div>
                        <div class="row px-2 mt-16">
                              <div class="table-responsive custom-table hCustom">
                              <table class="table table-bordered">
                                 <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th [hidden]="!selectedColumns.includes('player_id')">
                                                            <div>{{'player_id' | labelName}}</div>
                                                        </th>
                                                        <th [hidden]="!selectedColumns.includes('player_name')">
                                                            <div>{{'player_name' | labelName}}</div>
                                                        </th>
                                                        <th [hidden]="!selectedColumns.includes('agent_name')">
                                                            <div>{{'agent_name' | labelName}}</div>
                                                        </th>
                                                        <th [hidden]="!selectedColumns.includes('round_id')">
                                                            <div>
                                                              {{'round_id' | labelName}}
                                                                <app-ordering-arrow [column]="'round_id'"
                                                                    [params]="params"
                                                                    (changeOrder)="setOrder($event)"></app-ordering-arrow>
                                                            </div>
                                                        </th>
                                                        <th

                                                            [hidden]="!selectedColumns.includes('transaction_id')"
                                                            >
                                                            <div>
                                                              {{'transaction_id' | labelName}}
                                                                <app-ordering-arrow [column]="'transaction_id'"
                                                                    [params]="params"
                                                                    (changeOrder)="setOrder($event)"></app-ordering-arrow>
                                                            </div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('type')"
                                                            >
                                                            <div>{{'type' | labelName}}<!-- <app-ordering-arrow [column]="'type'" [params]="params"
                                                                (changeOrder)="setOrder($event)"></app-ordering-arrow> -->
                                                            </div>
                                                        </th>
                                                        <th [hidden]="!selectedColumns.includes('action_type')">
                                                            <div>
                                                              {{'action_type' | labelName}}
                                                                                                         </div>
                                                        </th>
                                                        <th [hidden]="!selectedColumns.includes('game_type')" >
                                                            <div>
                                                              {{'game_type' | labelName}}
                                                                <!-- <app-ordering-arrow [column]="'game_name'" [params]="params"
                                                                (changeOrder)="setOrder($event)"></app-ordering-arrow> -->
                                                            </div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('table_id')">
                                                            <div>{{'table_id' | labelName}}</div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('activity_timestamp')">
                                                            <div>
                                                              {{'activity_timestamp' | labelName}}
                                                                <app-ordering-arrow [column]="'created_at'"
                                                                    [params]="params"
                                                                    (changeOrder)="setOrder($event)"></app-ordering-arrow>
                                                            </div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('initial_balance') ">
                                                            <div>{{'initial_balance' | labelName}}</div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('bet_withdraw') ">
                                                            <div>{{'bet_withdraw' | labelName}}</div>
                                                        </th>
                                                        <th [hidden]="!selectedColumns.includes('win_deposit') ">
                                                            <div>{{'win_deposit' | labelName}}</div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('ending_balance') ">
                                                            <div>{{'ending_balance' | labelName}}</div>
                                                            </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('revenue') ">
                                                            <div>{{'revenue' | labelName}}&nbsp;
                                                                <i class="fa fa-info-circle text-muted"
                                                                    data-bs-toggle="tooltip" data-bs-placement="right"
                                                                    title="Ending Balance - Initial Balance">
                                                                </i>
                                                            </div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('status') ">
                                                            <div>{{'status' | labelName}}</div>
                                                        </th>
                                                        <th
                                                            [hidden]="!selectedColumns.includes('internal_tracking_id') ">
                                                            <div>
                                                              {{'internal_tracking_id' | labelName}}
                                                                <app-ordering-arrow [column]="'internal_tracking_id'"
                                                                    [params]="params"
                                                                    (changeOrder)="setOrder($event)"></app-ordering-arrow>
                                                            </div>
                                                        </th>
                                                    </tr>
                                                </thead>
                                <tbody>
                                                    <ng-container
                                                        *ngFor="let transaction of reports | paginate: { itemsPerPage: params.size, currentPage: p, totalItems: total,  id: 'playerpid' };  let i  = index">
                                 <tr>
                                                            <td
                                                                *ngIf="transaction?.maker_data || transaction?.checker_data; else noData">
                                                                <i (click)="toggleDetails(i)"
                                                                   [class]="selectedItemIndex === i ? 'fa fa-minus-circle' : 'fa fa-plus-circle'"
                                                                   style="cursor: pointer;">
                                                                </i>
                                                            </td>
                                                            <ng-template #noData>
                                                                <td>
                                                                    -
                                                                </td>
                                                            </ng-template>
                                                            <td
                                                                [hidden]="!selectedColumns.includes('player_id')">
                                                                {{ transaction?.user_id || '' }}
                                                            </td>
                                                            <td
                                                                [hidden]="!selectedColumns.includes('player_name')">
                                                                <span *ngIf="transaction?.user_name">
                                                                    <!-- <a href="javascript:void(0);"
                                                                        title="Click to Search"
                                                                        (click)="selectAgent(transaction?.user_name)"> -->
                                                                        {{ transaction?.user_name || '' }}
                                                                    <!-- </a>&nbsp;&nbsp; -->
                                                                    <span *ngIf="isBot(transaction)"
                                                                        class="badge bg-dark ms-2">{{'bot' | labelName}}</span>
                                                                </span>
                                                                <span *ngIf="!transaction?.user_name">
                                                                    <!-- <a href="javascript:void(0);"
                                                                        title="Click to Search"
                                                                        (click)="selectAgent(transaction.user_name)"> -->
                                                                        {{ transaction?.user_name
                                                                        || '' }}
                                                                    <!-- </a> -->
                                                                </span>
                                                            </td>
                                                            <td
                                                                [hidden]="!selectedColumns.includes('agent_name')">
                                                                <!-- <span *ngIf="transaction?.agent_name">
                                                                    <a href="javascript:void(0);"
                                                                        title="Click to Search"
                                                                        (click)="selectAgent(transaction?.agent_name)"> -->
                                                                        {{ transaction?.agent_name || '' }}
                                                                    <!-- </a> -->
                                                                <!-- </span> -->
                                                            </td>
                                                            <td
                                                                [hidden]="!selectedColumns.includes('round_id')">
                                                                <span *ngIf="transaction?.round_id">
                                                                    <div class="round-id-container">

                                                                        <a href="javascript:void(0);"
                                                                            title="Click to Search"
                                                                            (click)="selectAgent(transaction?.round_id, 'round_id')">
                                                                            {{ transaction?.round_id || '' }}
                                                                        </a>
                                                                        <a (click)="copyToClipboard(transaction.round_id)"
                                                                            title="Copy to clipboard" class="copy-link">
                                                                            <i class="fas fa-copy copy-icon"></i>
                                                                        </a>
                                                                    </div>
                                                                </span>
                                                            </td>

                                                            <td
                                                                [hidden]="!selectedColumns.includes('transaction_id')">
                                                                <span *ngIf="transaction?.transaction_id">
                                                                    <a href="javascript:void(0);"
                                                                        title="Click to Search"
                                                                        (click)="selectAgent(transaction?.transaction_id, 'transactionId')">
                                                                        {{ transaction?.transaction_id || '' }}
                                                                    </a>
                                                                </span>

                                                            </td>
                                                            <td
                                                                [hidden]="!selectedColumns.includes('type')">
                                                                {{ transaction?.type }}</td>
                                                            <td [hidden]="!selectedColumns.includes('action_type')">
                                                                {{ getActionTypeDisplay(transaction?.transaction_type_text)  || 'NA' }}
                                                            </td>
                                                            <td [hidden]="!selectedColumns.includes('game_type')">
                                                               <ng-container *ngIf="['Jetfair', 'Powerplay', 'TurboStars'].includes(transaction.game_provider); else gameName">
                                                            <i class="fa fa-info-circle" (click)="openGameNames(transaction.debit_transaction_id)" data-toggle="modal" data-target="#market-details" aria-hidden="true"></i>
                                                          </ng-container>
                                                          <ng-template #gameName>
                                                              {{ transaction?.game_name || '' }}
                                                          </ng-template></td>
                                                            <td [hidden]="!selectedColumns.includes('table_id')">
                                                                {{ transaction?.table_id || '' }}
                                                            </td>
                                                            <td [hidden]="!selectedColumns.includes('activity_timestamp')">
                                                                {{ transaction?.created_at || '' }}
                                                            </td>
                                                            <td [hidden]="!selectedColumns.includes('initial_balance')">
                                                                <ng-container *ngIf="transaction?.initial_balance">
                                                                    <!-- <img class="custom-currency-icon"
                                src="../../../../assets/dist/svg/{{transaction?.currency | lowercase}}_white.svg"
                                alt=""> -->
                                                                    <!-- {{ (transaction?.initial_balance | amountFormat: transaction?.currency : '': false) }} -->
                                                                    <span
                                                                        [innerHTML]="(transaction?.initial_balance | amountFormat: transaction?.currency : '')"></span>
                                                                </ng-container>
                                                            </td>
                                                            <td [hidden]="!selectedColumns.includes('bet_withdraw')">
                                                                {{ (transaction?.deducted_amount | amountFormat:
                                                                transaction?.currency : '': false) }}&nbsp;
                                                                <i *ngIf="transaction?.source_currency &&
                                                                    transaction?.target_currency &&
                                                                    transaction.source_currency !== transaction.target_currency
                                                                " data-toggle="tooltip" data-html="true" [attr.title]="
                                                                    'Source Currency: ' + transaction.source_currency + '&#13;Target Currency: ' +
                                                                    transaction.target_currency
                                                                " class="fas fa-info-circle"></i>
                                                            </td>
                                                            <td [hidden]="!selectedColumns.includes('win_deposit')">
                                                                {{ (transaction?.added_amount | amountFormat:
                                                                transaction?.currency : '': false) }}&nbsp;
                                                                <i *ngIf="transaction?.source_currency &&
                                                                    transaction?.target_currency &&
                                                                    transaction.source_currency !== transaction.target_currency
                                                                " data-toggle="tooltip" data-html="true" [attr.title]="
                                                                    'Source Currency: ' + transaction.source_currency + '&#13;Target Currency: ' +
                                                                    transaction.target_currency
                                                                " class="fas fa-info-circle"></i>
                                                            </td>

                                                            <td
                                                                [hidden]="!selectedColumns.includes('ending_balance') ">
                                                                <ng-container *ngIf="transaction?.ending_balance">
                                                                    <!-- <img class="custom-currency-icon"
                                src="../../../../assets/dist/svg/{{transaction?.currency | lowercase}}_white.svg"
                                alt=""> -->
                                                                    <!-- {{ (transaction?.ending_balance | amountFormat: transaction?.currency : '': false) }} -->
                                                                      <span [innerHTML]="(transaction?.ending_balance | amountFormat: transaction?.currency : '')"></span>
                                                                </ng-container>
                                                            </td>
                                                            <td
                                                                [hidden]="!selectedColumns.includes('revenue') "
                                                                [ngStyle]="{'color': (transaction.revenue < 0) ? 'red' : '' }">
                                                                <ng-container *ngIf="transaction?.revenue">
                                                                    <!-- <img class="custom-currency-icon"
                                src="../../../../assets/dist/svg/{{transaction?.currency | lowercase}}_white.svg"
                                alt=""> -->
                                                                    <!-- {{ (transaction?.revenue | amountFormat: transaction?.currency : '': false) }} -->
                                                                    <span [innerHTML]="(transaction?.revenue | amountFormat: transaction?.currency : '')"></span>
                                                                </ng-container>
                                                            </td>
                                                            <td
                                                                [hidden]="!selectedColumns.includes('status') ">
                                                                {{ transaction?.status || ''
                                                                }}</td>

                                                            <td
                                                                [hidden]="!selectedColumns.includes('internal_tracking_id') ">
                                                                {{ transaction?.internal_tracking_id || ''}}
                                                            </td>
                                                        </tr>
                                     <ng-container
                                                            *ngIf="selectedItemIndex === i && (transaction?.maker_data || transaction?.checker_data)">
                                                            <tr *ngIf="transaction?.maker_data">
                                                                <th colspan="3"><b>{{'maker_email' | labelName}}</b></th>
                                                                <td colspan="2">{{ getMakerCheckerData(transaction,
                                                                    'email',
                                                                    'maker') }}</td>
                                                            </tr>
                                                            <tr *ngIf="transaction?.maker_data">
                                                                <th colspan="3"><b>{{'maker_username' | labelName}}</b></th>
                                                                <td colspan="2">{{ getMakerCheckerData(transaction,
                                                                    'user_name', 'maker') }}</td>
                                                            </tr>
                                                            <tr *ngIf="transaction?.maker_data">
                                                                <th colspan="3"><b>{{'maker_time_stamp' | labelName}}</b></th>
                                                                <td colspan="2">{{ getMakerCheckerData(transaction,
                                                                    'time_stamp', 'maker') }}</td>
                                                            </tr>

                                                            <tr *ngIf="transaction?.checker_data">
                                                                <th colspan="3"><b>{{'checker_email' | labelName}}</b></th>
                                                                <td colspan="2">{{ getMakerCheckerData(transaction,
                                                                    'email',
                                                                    'checker') }}</td>
                                                            </tr>
                                                            <tr *ngIf="transaction?.checker_data">
                                                                <th colspan="3"><b>{{'checker_username' | labelName}}</b></th>
                                                                <td colspan="2">{{ getMakerCheckerData(transaction,
                                                                    'user_name', 'checker') }}</td>
                                                            </tr>
                                                            <tr *ngIf="transaction?.checker_data">
                                                                <th colspan="3"><b>{{'checker_time_stamp' | labelName}}</b></th>
                                                                <td colspan="2">{{ getMakerCheckerData(transaction,
                                                                    'time_stamp', 'checker') }}</td>
                                                            </tr>
                                                        </ng-container>
                                                        </ng-container>
                                                        <tr *ngIf="firstTimeApiCall && reports && reports.length === 0">
                                                        <td colspan="17" class="tnodata"> {{'no_records_found' | labelName}}
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="!firstTimeApiCall">
                                                        <td colspan="17" class="tnodata"> {{'click_search_to_load_data' | labelName}}
                                                        </td>
                                                    </tr>
                                </tbody>
                              </table>
                            </div>

                    <div class="custom-pagination" *ngIf="total>0">
                      <div class="cusP1">
                        <div class="custom-pagination pt-1">
                          <div class="pagination-item-list">
                            <pagination-controls id="playerpid" (pageChange)="pageChanged($event)"
                              (pageChange)="p = $event" [nextLabel]=this.permissionService.PAGINATION.NEXT [previousLabel]=this.permissionService.PAGINATION.PREVIOUS [maxSize] = this.permissionService.PAGINATION.MAX_SIZE></pagination-controls>
                            <select class="form-control" id="size" (change)="submitFilter()" [(ngModel)]="params.size">
                              <option *ngFor="let size of pageSizes" [value]="size.name"> {{ size.name }} </option>
                            </select>
                          </div>
                        </div>
                      </div>
                      <div class="cusP2">
                        <p class="showPage" appPaginationDisplay
                           [currentPage]="p"
                           [itemsPerPage]="params.size"
                           [totalItems]="total">
                        </p>
                      </div>

                      <!--Total Record-{{total}}-->
                    </div>
                    </div>
                    </div>
                </div>

              </div>

            </div>
            <!-- /.card -->

          </div>
    </section>
    <!-- /.content -->
  </div>

<ng-template #loader>
    <div class="w-full text-center custom-loader-div d-flex justify-content-center mt-4">
        <!-- <img class="chart-loader" src="./assets/dist/gif/loader.gif" /> -->
        <div class="custom-loader"></div>
    </div>
</ng-template>



<!-- ALL FILTERS USED IN DASHBOARD -->
<ng-template #reusableFilterBlock let-filterKey="filterKey" let-mainFilter="mainFilter">

    <ng-container *ngFor="let item of customFilters[filterKey]">

          <switch [ngSwitch]="item"  [ngClass]="{ 'col-md ': mainFilter }">

            <ng-container *ngSwitchCase="'userId'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">

                 <label for="user">{{'user_id' | labelName}}</label>
                 <select class="form-control" [attr.data-placeholder]="'select' | labelName" search_select2 [WithData]="WithData" [url]="searchUserUrl"
                   id="user" (onSelect)="selectUser($event)"></select>
                </div>

            </ng-container>

            <ng-container *ngSwitchCase="'transactionId'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">
                 <label for="search">{{'transaction_id' | labelName}}</label>
                 <input
                   type="text"
                   class="form-control"
                   id="search"
                   (keyup)="filter($event)"
                   [(ngModel)]="params.transactionId"
                   [placeholder]="'Search'"
                   title="Search Transaction Id"
                   />

                </div>

            </ng-container>

            <ng-container *ngSwitchCase="'roundId'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">
                 <label for="search">{{'round_id' | labelName}}</label>
                 <input
                     type="text"
                     class="form-control fnt-14"
                     id="search"
                     [(ngModel)]="params.roundId"
                     [placeholder]="'search' | labelName"
                 />

                </div>

            </ng-container>
            <ng-container *ngSwitchCase="'player_type'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">

                 <label for="player_type">{{'player_type' | labelName}}</label>
                 <select class="form-control" id="player_type" (change)="filter($event)"
                     [(ngModel)]="params.player_type">
                     <option value="">{{'select' | labelName}}</option>
                     <option value="all">{{'all_organization_players' | labelName}}</option>
                     <option value="direct">{{'only_direct_players' | labelName}}</option>
                 </select>
                </div>

            </ng-container>
            <ng-container *ngSwitchCase="'agent_id'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">

                 <label for="agent_id">{{'agent' | labelName}}</label>
                 <select class="form-control" select2 id="agent_id" title="Select"
                     (onSelect)="filterSelectAgent($event)" [(ngModel)]="params.agent_id"
                     [attr.data-placeholder]="'select' | labelName">
                     <!-- <option value="">{{'select_agent' | labelName}}</option> -->
                     <option *ngFor="let adminUser of userList" [value]="adminUser.id">
                         {{adminUser.first_name}} {{adminUser.last_name}}
                     </option>
                 </select>
                </div>

            </ng-container>
             <ng-container *ngSwitchCase="'currency'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">

                 <label for="currency">
                  {{'currency' | labelName}}&nbsp;
                    <i data-toggle="tooltip"
                        [title]="tenantConfigDescription['CurrencyFilter']"
                        class="fas fa-info-circle"
                    ></i>
                </label>
                <select class="form-control" title="Select"
                    [attr.data-placeholder]="'select' | labelName" id="currency" (change)="filter($event)"
                    [(ngModel)]="params.currency">
                    <option value="">{{'select' | labelName}}</option>
                    <option *ngFor="let currency of currencies" [value]="currency.code"> {{
                        currency.code }}
                    </option>
                </select>
                </div>

            </ng-container>
             <ng-container *ngSwitchCase="'action_type'">
                <div class="form-group multiple-selection-group"  [ngClass]="{
                    'select-form':  isDeepMatchValues(initialFilters[item], params[item])
                 }">
                 <label for="action_type ">{{'action_type' | labelName}}</label>

                  <select class="form-control" id="action_type" select2
                                            (onSelect)="selectActionType($event)" multiple
                                            data-placeholder="Select Action Type">
                                            <option *ngFor="let transactionType of filterActionType(params.action_category)"
                                            [value]="transactionType.title"> {{ transactionType.name | capitalize }} </option>
                                            <!-- <option value="">Action Type</option> -->
                                            <!-- <option value="bet">Bet</option>
                                            <option value="bet_non_cash">Bet non cash</option>
                                            <option value="deposit">Deposit</option>
                                            <option value="deposit_bonus_claim">Deposit bonus claim</option>
                                            <option value="referral_bonus_claim">Referral bonus claim</option>
                                            <option value="refund">Refund</option>
                                            <option value="refund_non_cash">Refund non cash</option>
                                            <option value="tip">Tip</option>
                                            <option value="tip_non_cash">Tip non cash</option>
                                            <option value="win">Win</option>
                                            <option value="win_non_cash">Win non cash</option>
                                            <option value="withdraw">Withdraw</option>
                                            <option value="withdraw_cancel">Withdraw cancel</option>
                                            <option value="exchange_place_bet_non_cash_debit"> place bet non
                                                cash debit</option>
                                            <option value="exchange_place_bet_cash_debit"> place bet cash debit
                                            </option>
                                            <option value="exchange_place_bet_cash_credit"> place bet cash
                                                credit</option>
                                            <option value="exchange_refund_cancel_bet_non_cash_debit"> refund
                                                cancel bet non cash debit</option>
                                            <option value="exchange_refund_cancel_bet_cash_debit"> refund cancel
                                                bet cash debit</option>
                                            <option value="exchange_refund_cancel_bet_non_cash_credit"> refund
                                                cancel bet non cash credit</option>
                                            <option value="exchange_refund_cancel_bet_cash_credit"> refund
                                                cancel bet cash credit</option>
                                            <option value="exchange_refund_market_cancel_non_cash_debit"> refund
                                                market cancel non cash debit</option>
                                            <option value="exchange_refund_market_cancel_cash_debit"> refund
                                                market cancel cash debit</option>
                                            <option value="exchange_refund_market_cancel_non_cash_credit">
                                                refund market cancel non cash credit</option>
                                            <option value="exchange_refund_market_cancel_cash_credit"> refund
                                                market cancel cash credit</option>
                                            <option value="exchange_settle_market_cash_credit"> settle market
                                                cash credit</option>
                                            <option value="exchange_settle_market_cash_debit"> settle market
                                                cash debit</option>
                                            <option value="exchange_resettle_market_cash_credit"> resettle
                                                market cash credit</option>
                                            <option value="exchange_resettle_market_cash_debit"> resettle market
                                                cash debit</option>
                                            <option value="exchange_cancel_settled_market_cash_credit"> cancel
                                                settled market cash credit</option>
                                            <option value="exchange_cancel_settled_market_cash_debit"> cancel
                                                settled market cash debit</option>
                                            <option value="exchange_deposit_bonus_claim">Exchange deposit bonus claim
                                            </option>
                                            <option *ngIf="isPlayerCategorisationEnable || (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0)" value="player_bulk_categorization_bonus">Loyalty bonus claimed</option>
                                            <option *ngIf="( affiliatedSmartico && this.permissionService.checkPermission('affiliated_system_smartico', 'R'))" value="royalty_non_cash_bonus">Loyalty non cash bonus</option>
                                            <option *ngIf="( affiliatedSmartico && this.permissionService.checkPermission('affiliated_system_smartico', 'R'))" value="royalty_cash_bonus">Loyalty cash bonus</option>
                                            <option *ngIf="(oneTimeBonus && this.permissionService.PERMISSIONS?.one_time_bonus?.R)"
                                                value="bet_one_time_bonus">Free Bets</option>
                                            <option *ngIf="(oneTimeBonus && this.permissionService.PERMISSIONS?.one_time_bonus?.R)"
                                                value="refund_one_time_bonus">Refund Free Bets</option> -->
                                            <!-- To do later as per client requirement -->
                                            <!-- <option value="withdraw_rejected_by_admin">Withdraw rejected by admin/provider</option> -->
                                        </select>

                </div>

            </ng-container>

            <ng-container *ngSwitchCase="'internal_error_code'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">
                 <div class="form-group">
                    <label for="internal_error_code1">{{'internal_error_code' | labelName}}</label>
                    <select class="form-control" id="internal_error_code1" (change)="filter($event)"
                       [attr.data-placeholder]="'select' | labelName"
                        [(ngModel)]="params.internal_error_code">
                        <option value="">{{'select_internal_error_code' | labelName}}</option>
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                    </select>
                </div>

                </div>

            </ng-container>  <ng-container *ngSwitchCase="'action_category'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">
                 <div class="form-group">
                     <label for="action_category">Action Category</label>
                    <select class="form-control" id="action_category"
                        [(ngModel)]="params.action_category" (change)="handleOnchange()">
                        <option value="">Select</option>
                        <option value="casino">Casino</option>
                        <option value="sports">Sports</option>
                    </select>
                </div>

                </div>

            </ng-container>
            <ng-container *ngSwitchCase="'type'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">
                  <label for="type">Type</label>
                <select class="form-control" id="type" (change)="filter($event)"
                    [(ngModel)]="params.type">
                    <option value="">Select</option>
                    <option value="financial">Financial</option>
                    <option value="game">Game</option>
                </select>

                </div>

            </ng-container>



            <ng-container *ngSwitchCase="'game_provider'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">
                 <label for="game_provider">{{'game_provider' | labelName}}</label>
                 <select select2 class="form-control" id="game_provider"
                 (onSelect)="filterGameTypeAccordingToProvider($event)"
                 [attr.data-placeholder]="'select' | labelName"
                 [(ngModel)]="params.game_provider">
                     <option value="">{{'select' | labelName}}</option>
                     <option *ngFor="let game of filterGameProviders(params.action_category)"
                         [value]="game"> {{ translateGame(game)}} </option>
                 </select>

                </div>

            </ng-container>  <ng-container *ngSwitchCase="'game_type'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">
                 <label for="game_type">{{'game_type' | labelName}}</label>
                 <select select2 class="form-control" id="game_type"
                     (onSelect)="filterGameType($event)" [attr.data-placeholder]="'select' | labelName"
                     [(ngModel)]="params.game_type">
                     <option value="">{{'select' | labelName}}</option>
                     <option *ngFor="let game of gamesToDisplay" [value]="game.name"> {{
                         game.name }} </option>
                 </select>

                </div>

            </ng-container>  <ng-container *ngSwitchCase="'time_type'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">

                 <label for="time_date_period">{{'DATE_RANGE' | labelName}}</label>
                 <select class="form-control" id="time_date_period"
                     (change)="filter($event,true);disbleSearch()"
                     [(ngModel)]="params.time_type">
                     <!-- <option value="">{{'select_time_period' | labelName}}</option> -->
                     <option value="today">{{'today' | labelName}}</option>
                     <option value="yesterday">{{'yesterday' | labelName}}</option>
                     <option value="weekly">{{'this_week' | labelName}}</option>
                     <option value="monthly">{{'this_month' | labelName}}</option>
                     <!-- <option value="yearly">{{'this_year' | labelName}}</option> -->
                     <option value="custom">{{'custom_date' | labelName}}</option>
                 </select>
                </div>

            </ng-container>

            <ng-container *ngSwitchCase="'time_period'">
                <div class="form-group"  [ngClass]="{
                    'select-form': isDeepMatchValues(initialFilters[item], params[item])
                 }">

                 <label for="time_period">{{'DATE_RANGE' | labelName}}</label>
                 <input autocomplete="off" data-placeholder="'search' | labelName" readonly
                     [placeholder]="'search' | labelName" type="text"
                     class="form-control float-right" id="time_period" [format]="format"
                     daterangetime_picker [maxDate]="maxDate"
                     (onSelect)="selectDateRange($event);updateSearch()" />
                </div>

            </ng-container>


            <ng-container *ngSwitchCase="'playerCategory'">
                <div class="form-group"  [ngClass]="{
                    'select-form': initialFilters[item] != params[item]
                 }">

                 <label for="sb_player_type">{{'player_type' | labelName}}</label>
                 <select class="form-control" id="sb_player_type" (change)="onPlayerTypeChange($event)" [(ngModel)]="params.playerCategory">
                   <option value="" selected disabled>{{'select' | labelName}}</option>
                   <option value="all_players">{{'all_players' | labelName}}</option>
                   <option value="real_players">{{'real_players' | labelName}}</option>
                   <option value="bot_players">{{'bot_players' | labelName}}</option>
                 </select>
                </div>

            </ng-container>


          <ng-container *ngSwitchCase="'additional_filter'">
            <!-- <div> -->
            <div class="form-group moreBtnGroup" *ngIf="filterGroups && filterGroups.length > 1">
              <button type="button" class="btn moreFilter" (click)="toggleFilterModal()">
                <p>{{'more_filters' | labelName}} <span *ngIf="filterCount != 0" class="filterCount"> {{filterCount}} </span></p>
                <img src="../../../../assets/dist/svg/filterIcon.svg" alt="filter">
              </button>
            </div>
          </ng-container>
          <ng-container *ngSwitchDefault>
          </ng-container>
        </switch>


    </ng-container>

  </ng-template>

  <!-- filtersModal -->
  <filter-modal [show]="showFilterModal" (close)="toggleFilterModal()" (apply)="applyFilters()"
  title="Unified Transaction Report"
    (reset)="resetFilter()">
    <ng-container *ngFor="let filter of filterGroups">
      <ng-container *ngIf="customFilters[filter]?.length > 0 && filter !== 'MAIN_FILTERS' && filter !== 'BUTTON'">
        <div class="innerFilterBodyWrap">
          <h6>{{filter | labelName }}</h6>
          <div class="innerFilterWrap">
            <ng-container
              *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: false }"></ng-container>
          </div>
        </div>
      </ng-container>
    </ng-container>
  </filter-modal>

<ng-template #filtersSearch>
  <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn" (click)="resetFilter()">
    <i class="fa fa-refresh"></i> {{'reset_filter' | labelName}}
  </button>
  <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn searchBtn fnt-12" (click)="submitFilter()">
    <i class="fa fa-search"></i> {{'search' | labelName}}
  </button>
</ng-template>
<!-- Market Modal -->
<div class="modal fade custom-modal" id="market-details" tabindex="-1" aria-hidden="true" style="display: none;">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title"> Market Names </h4>
          <button type="button" class="close edit-btn-grp" data-dismiss="modal" aria-label="Close">
            <i class="fa fa-times" aria-hidden="true"></i>
          </button>
        </div>
  
        <div class="modal-body" style="max-height: 600px; overflow-y: auto;">
          <table class="table table-striped text-center">
            <tbody>
              <ng-container *ngIf="marketNames.length > 0; else noDataFound">
                <tr *ngFor="let data of marketNames">
                  <td>{{ data }}</td>
                </tr>
              </ng-container>
              <ng-template #noDataFound>
                <tr>
                  <td>No Data Found</td>
                </tr>
              </ng-template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  
