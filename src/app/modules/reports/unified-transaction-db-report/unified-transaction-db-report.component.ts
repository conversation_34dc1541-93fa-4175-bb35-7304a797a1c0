
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import * as moment from 'moment-timezone';
import { Cur<PERSON>cy, Role } from 'src/app/models';
import { PermissionService } from 'src/app/services/permission.service';
import { domainDetails, generateApiUrl, getDomainNameFromHost, getSuperTenantsList, isDeepMatch } from 'src/app/services/utils.service';
import { convertKeysToCamelCase, GetAllTransactionTypes, getTransactionType, isDateRangeWithinDuration, PageSizes, TenantConfigDescription, TIMEZONE } from 'src/app/shared/constants';
import { AdminAuthService } from '../../admin/services/admin-auth.service';
import { SuperAdminAuthService } from '../../super-admin/services/super-admin-auth.service';
import { TenantService } from '../../tenant/tenant.service';
import { ReportService } from '../reports.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FilterConfig, FilterService } from 'src/app/services/filter.service';
import { BaseFilterComponent } from 'src/app/shared/components/base-filter/base-filter.component';
import { Select2ResetService } from 'src/app/services/select2-reset.service';

declare const $: any;
declare const toastr: any;

@Component({
  selector: 'app-unified-transaction-db-report',
  templateUrl: './unified-transaction-db-report.component.html',
  styleUrls: ['./unified-transaction-db-report.component.scss', '../report.component.scss']
})
export class UnifiedTransactionDbReportComponent extends  BaseFilterComponent implements OnInit, OnDestroy {
  // Properties specific to this component
  maxDate: any = new Date().setHours(23,59,59,59);
  minDate:any = moment().subtract(180, 'days').startOf('day');

  isSubAgentModuleAllowed:boolean = true;
  isPrimaryCurrencyDataEnable:boolean = true;
  roles: string = localStorage.getItem('roles') || '';
  isAgent:boolean = false;
  reports: any[] = [];
  p: number = 1;
  tenantMenuList:any[] = []
  currencies: Currency[] = [];
  format: string = "YYYY-MM-DD HH:mm:ss";
  userList :any[]=[];
  tenantsList :any[]=[];
  gameProvider: any[] = [];
  gameType: any[] = [];
  gamesToDisplay: any[] = []
  tenantsOwnerList :any[]=[];
  total_bets: number = 0;
  total_deposit: number = 0;
  total_wins: number = 0;
  total_withdraw: number = 0;
  total: number = 0;
  tip: number = 0;
  tipCount: number = 0;
  totalBetRefund: number = 0;
  totalWinRefund: number = 0;
  pageNextTotal: number = 0;
  pageCurrentTotal: number = 0;
  tenantBaseCurrency:any = '';

  selectedItemIndex: number | null = null;
  expanded:boolean=false;
  hideShow:boolean = true;
  filteredRes:any[] = []
  dataTableParams: any = {
    searching: false,
    info: true,
    lengthChange: true,
    autoWidth: false,
    responsive: true,
    "dom": '<"top"lp>t<"bottom"ifp><"clear">',
    lengthMenu: PageSizes.map(m => m.name),
    buttons: [{ extend: 'csv', text: 'Export as CSV', className: 'ctsmcsv mgt btn btn-info mt-0' }]
  };
  allReports: any[] = [];
  allColumns:any[]=[];
  oneTimeBonus:boolean = false;
  affiliatedSmartico:boolean = false;
  selectedColumns:any[]=['all', 'player_id', 'player_name', 'agent_name', 'round_id', 'transaction_id', 'type', 'action_type', 'game_type', 'table_id', 'activity_timestamp', 'initial_balance', 'bet_withdraw', 'win_deposit', 'ending_balance', 'revenue', 'status', 'internal_tracking_id'];
  sameAsOldParams:boolean = true
  oldParams:any = {}
  searchUserUrl: string = '';
  tenantConfigDescription = TenantConfigDescription;

  breadcrumbs: Array<any> = [
    { title: 'Home', path: '/super-admin' },
    { title: 'Unified Transaction Report', path: '/reports/unified-report' },
  ];

  // Properties already defined in BaseFilterComponent are not redeclared:
  // - maxDate, pageSizes, TIMEZONE, zone, isLoader, firstTimeApiCall, customDate, showFilterModal, filterCount

  // Initialize customFilters and filterGroups to avoid template errors
  customFilters: any = {};
  filterGroups: any[] = [];

  disableRequest: boolean = false;
  isPlayerCategorisationEnable: boolean = false;
  reporting_email: any;
  email_verified: boolean = false;
  secondBackOfficeDomainExistsForTenant : boolean = false;
  isSuperAdmin:boolean=this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0;
  initialFilters: any = {
    userId: '',
    transactionId: '',
    roundId: '',
    size: 10,
    page: 1,
    agent_id: '',
    currency: '',
    player_type: 'all',
    isDirectPlayer: '',
    time_zone: 'UTC +00:00',
    time_zone_name: 'UTC +00:00',
    tenantId: '',
    action_category: '',
    owner_id: '',
    action_type: '',
    type: '',
    time_type: 'today',
    internal_error_code: '',
    game_provider: '',
    game_type: '',
    time_period: '',
    datetime:{},
    order: 'desc',
    sort_by: 'created_at',
    playerCategory : (this.permissionService.checkBotDomain() == false) ? 'all_players' : 'real_players' ,
  };
  params: any = {...this.initialFilters};
  transactionTypes = GetAllTransactionTypes;
  marketNames:any[] = [];
  totalCashDeposit: any;
  totalNonDeposit: any;
  totalCashWithdraw: any;
  totalNonCashWithdraw: any;
  totalWithdrawCancel: any;

  constructor(private ReportService: ReportService,
              public adminAuthService: AdminAuthService,
    private select2ResetService: Select2ResetService,
              private tenantService: TenantService,
               public router:Router,
    public route:ActivatedRoute,
    public superAdminAuthService: SuperAdminAuthService,
    public override permissionService: PermissionService,
    public override filterService: FilterService
  ) {
    super(filterService, permissionService, adminAuthService);
    this.params = { ...this.params, ...this.ReportService.uTParams };
    this.isSubAgentModuleAllowed = (!!this.permissionService.MODULES?.subAgent ? true : false);
    this.isPrimaryCurrencyDataEnable = (!!this.permissionService.MODULES?.primaryCurrencyDataEnable ? true : false);
    this.isPlayerCategorisationEnable = (!!this.permissionService.MODULES?.playerCategorization ? true : false);
    const userData = JSON.parse(localStorage.getItem('userData') || '{}');

    this.reporting_email = userData ? userData.reporting_email : null;
   this.email_verified = userData ? userData.reporting_email_verified : null;

  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.getProvider()
    this.oneTimeBonus = (localStorage.getItem('allowedModules')?.includes('oneTimeBonus') ? true : false);
    this.affiliatedSmartico = (localStorage.getItem('allowedModules')?.includes('affiliatedSystemSmartico') ? true : false);

    if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {
      this.getTenantList();
      this.hideShow = false;
    } else {
    // this.getReport();
      this.getCurrencyList();
      this.getAgentUserList();
    }
    this.getProvider()
    this.resetFilter(true);
    // Initialize filters
    this.getAllFilters();
    this.columnPermission();
    this.selectedColumnsList(this.selectedColumns);

  if (this.adminAuthService.adminTokenValue && this.adminAuthService.adminTokenValue.length > 0) {
      if(this.roles) {
        const roles = JSON.parse(this.roles);
        if(roles && roles.findIndex( (role: any) => role === Role.Admin ) == -1 && roles.findIndex( (role: any) => role === Role.Agent ) > -1) {
          this.isAgent = true;
        }

      }

    }
    this.pageNextTotal = this.params.size;
    this.pageCurrentTotal = this.p;
    this.updateSearchUserUrl();

    if (this.isPlayerCategorisationEnable || (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0)) {
      if (!this.transactionTypes.find((v: any) => { v.value == 41 }) && !this.transactionTypes.some(item => item.title === 'player_bulk_categorization_bonus')) {
        this.transactionTypes.push({ name: 'Loyalty bonus claimed', title: 'player_bulk_categorization_bonus', value: 41, category: 'financial' });
      }
    }

    if (this.oneTimeBonus && this.permissionService.PERMISSIONS?.one_time_bonus?.R) {
      if (!this.transactionTypes.some(item => item.title === 'bet_one_time_bonus')) {
        this.transactionTypes.push({ name: 'Free Bets', title: 'bet_one_time_bonus', value: 46, category: 'financial' });
      }

      if (!this.transactionTypes.some(item => item.title === 'refund_one_time_bonus')) {
        this.transactionTypes.push({ name: 'Refund Free Bets', title: 'refund_one_time_bonus', value: 47, category: 'financial' });
      }
      if (!this.transactionTypes.some(item => item.title === 'free_bets_on_deposit_bonus_claim')) {
        this.transactionTypes.push({ name: 'Free Bets On Deposit Bonus Claim', title: 'free_bets_on_deposit_bonus_claim', value: 68, category: 'financial' });
      }
      if (!this.transactionTypes.some(item => item.title === 'free_bets_on_promo_code_bonus_claim')) {
        this.transactionTypes.push({ name: 'Free Bets On Promo Code Bonus Claim', title: 'free_bets_on_promo_code_bonus_claim', value: 75, category: 'financial' });
      }

      if (this.permissionService.PERMISSIONS?.one_time_bonus_deposit?.R) {
        if (!this.transactionTypes.some(item => item.title === 'one_time_bonus_deposit')) {
          this.transactionTypes.push({ name: 'Free Bets Deposit', title: 'one_time_bonus_deposit', value: 48, category: 'financial' });
        }
      }

      if (this.permissionService.PERMISSIONS?.one_time_bonus_withdrawal?.R) {
        if (!this.transactionTypes.some(item => item.title === 'one_time_bonus_withdraw')) {
          this.transactionTypes.push({ name: 'Free Bets Withdrawal', title: 'one_time_bonus_withdraw', value: 49, category: 'financial' });
        }
      }
    }

    if ((this.affiliatedSmartico && this.permissionService.checkPermission('affiliated_system_smartico', 'R')) || (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0)) {
      if (!this.transactionTypes.some(item => item.title === 'royalty_non_cash_bonus')) {
        this.transactionTypes.push({ name: 'Loyalty Non Cash Bonus', title: 'royalty_non_cash_bonus', value: 44, category: 'financial' });
      }

      if (!this.transactionTypes.some(item => item.title === 'royalty_cash_bonus')) {
        this.transactionTypes.push({ name: 'Loyalty Cash Bonus', title: 'royalty_cash_bonus', value: 45, category: 'financial' });
      }
    }

    if(!(this.permissionService.PERMISSIONS?.sports_free_bet_deposit?.R && this.permissionService.PERMISSIONS?.sports_free_bet_withdrawal?.R)) {
      this.transactionTypes = this.transactionTypes.filter(transaction => ![
          'sports_free_bet_deposit',
          'sports_free_bet_withdraw',
        ].includes(transaction.title)
      );
    }

    if(!this.permissionService.PERMISSIONS?.sports_free_bet?.R) {
      this.transactionTypes = this.transactionTypes.filter(transaction => ![
        'place_bet_sports_freebet_debit',
        'refund_cancel_bet_sports_freebet_debit',
        'refund_cancel_bet_sports_freebet_credit',
        'refund_market_cancel_sports_freebet_debit',
        'refund_market_cancel_sports_freebet_credit',
        'cancel_settled_bet_sports_freebet_debit',
        'cancel_settled_bet_sports_freebet_credit',
        'sports_free_bets_deposit_bonus_claim',
        'sports_free_bets_promo_code_bonus_claim'
      ].includes(transaction.title));
    }

  }

  ngAfterViewInit(): void {
    $('#owner_id option:eq(0)').prop('selected',true);
    setTimeout(() => {
      $('#sb_player_type').val(this.params.playerCategory).trigger('change');
    },10)
  }

  getAgentUserList(){

    if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {

      this.ReportService.getSuperAdminUserList({"owner_id":this.params.owner_id,"tenant_id":this.params.tenantId}).subscribe((res: any) => {
        this.userList = res.record;
      });
    } else {
      this.ReportService.getAdminUserList().subscribe((res: any) => {
        this.userList = res.record;
      });
    }

  }

  updateSearchUserUrl(): void {
    this.searchUserUrl = this.isSuperAdmin
      ? generateApiUrl() + `super/admin/transactions/users/new2?owner_type=User&tenant_id=${this.params.tenantId}`
      : generateApiUrl() + `admin/transactions/users/new2?owner_type=User`;
  }

  getTenantList() {

    if (this.superAdminAuthService.superAdminTokenValue &&
        this.superAdminAuthService.superAdminTokenValue.length > 0) {

          this.tenantsList = getSuperTenantsList();
          if (this.tenantsList.length > 0) {
              this.params.tenantId = 0;
              this.getTenantOwnerList(0);
              this.getCurrencyList('0');
              this.checkSecondBackOfficeDomainExists(this.params.tenantId);
          }
    }
}

    getTenantOwnerList(id:number) {
        if(this.superAdminAuthService.superAdminTokenValue &&
            this.superAdminAuthService.superAdminTokenValue.length > 0) {
            this.ReportService.getSuperTenantsOwnerList({id}).subscribe((res: any) => {
                this.tenantsOwnerList = res.record;
                this.params.ownerId = this.tenantsOwnerList.length > 0 ? this.tenantsOwnerList[0].id : '';
                $('#owner_id option:eq(0)').prop('selected',true);
                this.disableRequest = false;
            });
        }else{
          this.disableRequest = false;
        }
    }

  tenantFilter(evnt: any) {
    // this.p = 1;
    // this.params.page = this.p;
    this.disableRequest = true;
    this.params.tenantId = parseInt(evnt);
    this.tenantService.getTenant(this.params.tenantId).subscribe((res: any) => {
      this.tenantMenuList = res?.record?.menu_setting?.filter((item: { selectValue: any; active: any }) => item.active == true && item.selectValue == true).map((item: { name: any; }) => item.name);
    });
   // this.params.playerCategory = 'real_players';
    this.checkSecondBackOfficeDomainExists(this.params.tenantId)
    this.getTenantOwnerList(evnt);
    this.getCurrencyList(evnt);

    if(evnt != 0){
      this.hideShow = true;
      this.getAgentUserList();
    }
    else{
      this.hideShow = false;
      this.userList = [];
    }
    this.getAllFilters()
  }

  getCurrencyList(evnt=''){
    if(this.superAdminAuthService.superAdminTokenValue &&
        this.superAdminAuthService.superAdminTokenValue.length > 0) {

      this.ReportService.getCurrencyList(evnt).subscribe((res: any) => {
        this.currencies = res.record;
      });

    }else {

      this.ReportService.getCurrencyList().subscribe((res: any) => {
        this.currencies = res.record;
      });
    }
  }


  selectAgent(searchValue: any, type: string) {

    this.params = {
      ...this.params,
      [type === 'transactionId' ? 'transactionId' : 'roundId']: searchValue
    };
  }

  convertToUTC(inputDate: string, inputTimezone: string): string {
    const date = moment.tz(inputDate, inputTimezone);
    const utcDate = date.utc().format('YYYY-MM-DD HH:mm:ss');
    return utcDate;
  }

  getReport() {
    let { hasAccess, noAccessTenantIds } = this.permissionService.managerAllPermissionAvailable(this.params.tenantId, 'unified_transaction_report','R');
    if(!hasAccess){
      let tenantNames = this.tenantsList.filter((tenant: any) => noAccessTenantIds.includes(tenant.id)).map((tenant: any) => tenant.name).join(', ');
      toastr.error(`You don't have access for ${tenantNames}`);
      return;
    }

    this.filterService.updateTimezone(this.params, this.adminAuthService);
    let time_period = this.params.time_period
    let time_type = this.params.time_type
    if (time_type == 'custom' && !isDateRangeWithinDuration(time_period)) {
      toastr.error('Custom Date should be in range of 90 days');
      return;
    }
      this.isLoader = true;
      this.firstTimeApiCall = true;
      $('.ctsmcsv').remove();

    if(
        this.params.time_period?.start_date &&
        this.params.time_period?.end_date &&
        this.params.time_zone_name &&
        this.params.time_zone_name !== 'UTC +00:00') {
      this.params = {...this.params,
        datetime : {
        start_date: this.convertToUTC(this.params.time_period.start_date, this.params.time_zone_name),
        end_date:this.convertToUTC(this.params.time_period.end_date, this.params.time_zone_name)
      }}
    } else{
      this.params = {...this.params, datetime : {
        start_date: this.params.time_period?.start_date,
        end_date: this.params.time_period?.end_date
      }}
    }
    if(this.params.game_provider === ''){
      this.params.game_type = ''
    }
    super.applyFilters(this.params, this.initialFilters, () => {});

    let modifiedParams = this.getModifiedParams(this.params)
    modifiedParams.totals = false
    this.getRecords(modifiedParams)
    if (!this.sameAsOldParams) {
      this.getRecordCount(modifiedParams)
    }
    this.setPageCount(this.p);
  }

  getRecords(modifiedParams: any) {
    this.ReportService.getSuperAdminUnifiedDbReport(modifiedParams).subscribe(
      (res: any) => {
        this.reports = res?.data?.data?.result?.rows;
        this.isLoader = false;
      },
      (_error: any) => {
        // Prefix with underscore to indicate intentionally unused parameter
        this.isLoader = false;
        console.error('Error getting records');
      }
    );
  }

  getRecordCount(modifiedParams: any) {
    modifiedParams = { ...modifiedParams, count: true }
    this.ReportService.getSuperAdminUnifiedDbReport(modifiedParams).subscribe(
      (res: any) => {
        this.total = res?.data?.data?.result?.count;

        let limit = this.params.size;
        let offset = 1;
        if (this.p > 1) {
          offset = limit * (this.p - 1);
        }
        this.pageCurrentTotal = offset;

        this.pageNextTotal = (offset > 1 ? offset : 0) + parseInt(limit);
        if (this.total < this.pageNextTotal) {
          this.pageNextTotal = this.total;
        }
        this.isLoader = false;
      },
      (_error: any) => {
        // Prefix with underscore to indicate intentionally unused parameter
        this.isLoader = false;
        console.error('Error getting record count');
      }
    );
  }
  // Added function to get the modified parameters according to requirements
  getStartEndDate(input: any) {
    let response: any = '{}'
    if ((input.start_date || input.startDate) && (input.end_date || input.endDate)) {
      response = { startDate: (input.start_date || input.startDate), endDate: (input.end_date || input.endDate) }
    }
    return response;
  }

  getModifiedParams(params: any) {
    let modifiedParams = convertKeysToCamelCase(params)

    modifiedParams.dateTime = "{}"
    if (modifiedParams.timeType == 'custom') {
      modifiedParams.dateTime = (modifiedParams.datetime) ? JSON.stringify(this.getStartEndDate(modifiedParams.datetime)) : "{}"
      modifiedParams.timePeriod = (modifiedParams?.timePeriod) ? JSON.stringify(this.getStartEndDate(modifiedParams.timePeriod)) : "{}"
    }
    if (!modifiedParams.timePeriod || !modifiedParams?.timePeriod?.length) {
      modifiedParams.timePeriod = "{}"
    }

    if (modifiedParams.actionType || modifiedParams?.actionType?.length) {
      modifiedParams.actionType = (modifiedParams?.actionType || modifiedParams?.actionType?.length) ? JSON.stringify(modifiedParams.actionType) : modifiedParams.actionType
    }
    delete modifiedParams.datetime
    let modifiedParamswithoutPage = { ...modifiedParams }
    delete modifiedParamswithoutPage.page
    delete modifiedParamswithoutPage.size
    delete modifiedParamswithoutPage.totals
    delete modifiedParamswithoutPage.send_email
    this.sameAsOldParams = isDeepMatch(this.oldParams, modifiedParamswithoutPage)
    this.oldParams = { ...modifiedParams }
    delete this.oldParams.page
    delete this.oldParams.size
    delete this.oldParams.totals
    delete this.oldParams.send_email
    return modifiedParams
  }

  getReportTotals() {
    let { hasAccess } = this.permissionService.managerAllPermissionAvailable(this.params.tenantId, 'unified_transaction_report','R');
    if(!hasAccess){
      return;
    }
    let time_period = this.params.time_period
    let time_type = this.params.time_type
    if (time_type == 'custom' && !isDateRangeWithinDuration(time_period)) {
      // toastr.error('Custom Date should be in range of 90 days');
      return;
    }
    this.isLoader = true;
    this.firstTimeApiCall = true;
    $('.ctsmcsv').remove();
    this.filterService.updateTimezone(this.params, this.adminAuthService);

    if (
      this.params.time_period?.start_date &&
      this.params.time_period?.end_date &&
      this.params.time_zone_name &&
      this.params.time_zone_name !== 'UTC +00:00') {
      this.params = {
        ...this.params,
        datetime: {
          start_date: this.convertToUTC(this.params.time_period.start_date, this.params.time_zone_name),
          end_date: this.convertToUTC(this.params.time_period.end_date, this.params.time_zone_name)
        }
      }
    } else {
      this.params = {
        ...this.params, datetime: {
          start_date: this.params.time_period?.start_date,
          end_date: this.params.time_period?.end_date
        }
      }
    }
    if (this.params.game_provider === '') {
      this.params.game_type = ''
    }
    let modifiedParams = this.getModifiedParams(this.params)
    modifiedParams.totals = true
    if (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {
      this.ReportService.getSuperAdminUnifiedDbReport(modifiedParams).subscribe(
        (res: any) => {
          this.getPrepareRecordTotal(res);
        },
        (_error: any) => {
          // Prefix with underscore to indicate intentionally unused parameter
          this.isLoader = false;
          console.error('Error getting report totals');
        }
      );
    } else {
      this.ReportService.getAdminUnifiedDbReport(modifiedParams).subscribe(
        (res: any) => {
          this.getPrepareRecordTotal(res);
        },
        (_error: any) => {
          // Prefix with underscore to indicate intentionally unused parameter
          this.isLoader = false;
          console.error('Error getting report totals');
        }
      );
    }
  }

  getPrepareRecord(res:any){
    this.reports = res?.data?.data?.result?.rows;
    this.total = res?.data?.data?.result?.count;

    let limit = this.params.size;
    let offset = 1;
    if (this.p > 1) {
      offset = limit * (this.p - 1);
    }
    this.pageCurrentTotal = offset;

    this.pageNextTotal = (offset>1?offset:0) + parseInt(limit);
    if(this.total < this.pageNextTotal){
      this.pageNextTotal = this.total;
    }
    this.isLoader = false
  }

  getPrepareRecordTotal(res:any){

    let result = res?.data?.data?.result
    this.total_bets = result?.bet;
    this.total_deposit = result?.deposit;
    this.total_wins = result?.win;
    this.total_withdraw = result?.withdraw;
    this.tenantBaseCurrency = result?.baseCurrency
    this.totalCashDeposit = result?.total_cash_deposit
    this.totalNonDeposit = result?.total_non_deposit
    this.totalCashWithdraw = result?.total_cash_withdraw
    this.totalNonCashWithdraw = result?.total_non_cash_withdraw
    this.totalWithdrawCancel = result?.total_withdraw_cancel
    this.totalBetRefund = result?.bet_refund
    this.totalWinRefund = result?.win_refund

    this.isLoader = false
  }
  getAll() {
    if(this.disableRequest){ return }
    this.getReport();
    if (!this.sameAsOldParams) {
      this.getReportTotals()
    }
    this.setPageCount(this.p);
  }

  filter(evt: any, dateFilterType: boolean=false) {
    // Get the input element ID to determine which filter is being changed
    const targetId = evt.target?.id;

    // Handle specific filters based on the input ID
    if (targetId === 'transactionId') {
      this.params.transactionId = evt.target.value;
    } else if (targetId === 'search') {
      this.params.search = evt.target.value;
    } else if (targetId === 'roundId') {
      this.params.roundId = evt.target.value;
    } else if (targetId === 'player_type') {
      this.params.player_type = evt.target.value;
    } else if (targetId === 'currency') {
      this.params.currency = evt.target.value;
    } else if (targetId === 'internal_error_code') {
      this.params.internal_error_code = evt.target.value;
    } else if (targetId === 'action_category') {
      this.params.action_category = evt.target.value;
      if(evt.target.value === 'financial')
        this.params.action_category = "financial"
      else
        this.params.action_category = ''
    } else if (targetId === 'time_type') {
      this.params.time_type = evt.target.value;
        // Update all filters
      this.getAllFilters();
    }

    // Handle date filter type
    if(this.params.time_type =='custom'){
      this.customDate=true;
      if(dateFilterType){
        setTimeout(() => {
          $('#time_period').val('');
        }, 10);
      }
    } else {
      this.customDate=false;
    }


    this.handleOnchange();
    this.params = { ...this.params, action_category: ''};
  }

  handleOnchange() {
    $('#action_type').val('').trigger('change');
    this.params = { ...this.params, game_provider: '' }
    this.params = { ...this.params, action_type: '' }
  }

  submitFilter(){
    this.p = 1;
    this.params = { ...this.params, page: this.p};
    if(this.disableRequest){ return }
    this.getReport();
    if (!this.sameAsOldParams) {
      this.getReportTotals()
    }
    if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0){
      this.getProvider()
    }
    this.setPageCount(this.p);
  }

  setPageCount(page: number) {
    let limit = this.params.size;
    let offset = page;

    if (page > 1) {
      offset = limit * (page - 1);
    }
    this.pageCurrentTotal = offset;
    this.p = page;
    this.pageNextTotal = (offset > 1 ? offset : 0) + parseInt(limit);
    if (this.total < this.pageNextTotal) {
      this.pageNextTotal = this.total;
    }
  }

  filterSelectAgent(evt: any) {
    if(evt == null) return;
    this.params = {...this.params, agent_id:evt};
    // Call getAllFilters to update filters when agent_id changes
    this.getAllFilters();
  }

  selectDateRange(time_period: any) {
    this.params = {...this.params, time_period}
    // Call getAllFilters to update filters when time_period changes
    this.getAllFilters();
  }

  filterSelectOwner(evt: any) {
    this.params = {...this.params, owner_id:evt};
    // Call getAllFilters to update filters when owner_id changes
    this.getAllFilters();
  }




  pageChanged(page: number) {
    this.params = { ...this.params, page };
    if(this.disableRequest){ return }
    this.getReport();
    if (!this.sameAsOldParams) {
      this.getReportTotals()
    }
    this.setPageCount(page);
  }

  resetFilter(nodata=false) {

        this.p = 1;
        $('#action_type').val([]).trigger('change');
        $( "#user" ).val('').trigger('change');
        this.p = 1;
        this.params = { ...this.initialFilters };
        setTimeout(() => {
          $( "#agent_id" ).val('').trigger('change');
          $("#game_provider").val('').trigger('change')
          $("#game_type").val('').trigger('change')
          $('#sb_player_type').val(this.params.playerCategory).trigger('change');
          if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0){
            $( "#tenant_id" ).val(0).trigger('change');
          }
          $('.ctsmcsv').remove();
        },10)
      if(!nodata){
       this.getReport();
        if (!this.sameAsOldParams) {
          this.getReportTotals()
        }
        this.setPageCount(this.p);
        if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0){
          this.getProvider()
        }
      }


        this.disableRequest=false;
        this.getAllFilters();
        super.applyFilters(this.params, this.initialFilters, () => {});


        // $( "#time_zone" ).val('UTC +00:00').trigger('change');
    }


    download(action: 'download' | 'email'){


      this.filterService.updateTimezone(this.params, this.adminAuthService);
      let module = (action === 'download') ? 'export' : 'send_email';
      let { hasAccess, noAccessTenantIds } = this.permissionService.managerAllPermissionAvailable(this.params.tenantId, 'unified_transaction_report',module);
      if(!hasAccess){
        let tenantNames = this.tenantsList.filter((tenant: any) => noAccessTenantIds.includes(tenant.id)).map((tenant: any) => tenant.name).join(', ');
        toastr.error(`You don't have access for ${tenantNames}`);
        return;
      }

      this.filterService.updateTimezone(this.params, this.adminAuthService);

      if(
          this.params.time_period?.start_date &&
      this.params.time_period?.end_date &&
      this.params.time_zone_name &&
      this.params.time_zone_name !== 'UTC +00:00') {
        this.params = {...this.params,
          datetime : {
            start_date: this.convertToUTC(this.params.time_period.start_date, this.params.time_zone_name),
            end_date:this.convertToUTC(this.params.time_period.end_date, this.params.time_zone_name)
          }}
      } else{
        this.params = {...this.params, datetime : {
          start_date: this.params.time_period?.start_date,
          end_date: this.params.time_period?.end_date
        }}
      }
      let modifiedParams = this.getModifiedParams(this.params)

        if (action === 'email') {
          modifiedParams = { ...modifiedParams, send_email: true };
        } else {
          modifiedParams = { ...modifiedParams, send_email: false };
        }
      let data ={
        payload : JSON.stringify(modifiedParams),
        module : 'unified_transaction_report',
        type : 'unified_transaction_report_db'
      }
      this.ReportService.export(data).subscribe(
        (res: any) => {
          toastr.success(res.message || 'CSV generation has been successfully started... You can download the file in the export section.')
        });
  }


  setOrder(sort: any) {
    this.p = 1;
    this.params = { ...this.params, page: this.p, order: sort.order, sort_by: sort.column };
    if(this.disableRequest){ return }
    this.getReport();
    if (!this.sameAsOldParams) {
      this.getReportTotals()
    }
  }

  ngOnDestroy(): void {
    this.ReportService.uTParams = { ...this.ReportService.uTParams, ...this.params };
  }

  disbleSearch(){
    this.params.time_period=[];
    if(this.params.time_type=='custom' && this.params.time_period.length==0){
      this.disableRequest=true;
    }else{
      this.disableRequest=false;

    }
  }
  updateSearch(){
      this.disableRequest=false;
  }
  selectActionType(action_type: any) {
    if(action_type == null) action_type = []
    this.params= {
      ...this.params,
      action_type: action_type
    }
    // Call getAllFilters to update filters when action_type changes
    this.getAllFilters();
  }

  filterGameType(type:any){
    this.params= {
      ...this.params,
      game_type: type
    }
    // Call getAllFilters to update filters when game_type changes
    this.getAllFilters();
  }
  filterGameTypeAccordingToProvider(evt: any) {
    this.params= {
      ...this.params,
      game_provider: evt
    }
    this.params.game_type = ''
    this.gamesToDisplay = this.filteredRes.filter(game => game.page_title == evt)
    const seen = new Set();
    this.gamesToDisplay = this.gamesToDisplay.filter(obj => {
      if (seen.has(obj.name)) {
        return false;
      }
      seen.add(obj.name);
      return true;
    });
    // Call getAllFilters to update filters when game_provider changes
    this.getAllFilters();
  }
  getProvider() {
    if (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {

      this.ReportService.getSuperAdminProviders({'tenant_id':this.params.tenantId}).subscribe(
        (res: any) => {
          this.getPreparedProvider(res);
        },
        (_error: any) => {
          // Prefix with underscore to indicate intentionally unused parameter
          this.isLoader = false;
          console.error('Error getting providers');
        }
      );
    } else {
      this.ReportService.getAdminProviders().subscribe(
        (res: any) => {
          this.getPreparedProvider(res);
        },
        (_error: any) => {
          // Prefix with underscore to indicate intentionally unused parameter
          this.isLoader = false;
          console.error('Error getting providers');
        }
      );
    }
  }

  getPreparedProvider(res: any) {
    this.filteredRes = res.record
    this.gameProvider = Array.from(new Set(this.filteredRes.map(element => element.page_title)));
    if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Exchange')) {
      this.gameProvider.push('Jetfair')
    }
    if (this.adminAuthService?.tenantDataValue?.menuList?.includes('BTI SportsBook')) {
      this.gameProvider.push('bti_sportsbook')
    }
    if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Saba SportsBook')) {
      this.gameProvider.push('sbs_sportsbook')
    }
    if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Sap Exchange')) {
      this.gameProvider.push('sap_lobby')
    }
    if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Powerplay Exchange')) {
      this.gameProvider.push('Powerplay')
    }
    if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Turbo Stars')) {
      this.gameProvider.push('TurboStars')
    }
    if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0){
      if(this.params.tenantId){
        if (this.tenantMenuList?.includes('Exchange')) {
          this.gameProvider.push('Jetfair')
        }
        if (this.tenantMenuList?.includes('BTI SportsBook')) {
          this.gameProvider.push('bti_sportsbook')
        }
        if (this.tenantMenuList?.includes('Saba SportsBook')) {
          this.gameProvider.push('sbs_sportsbook')
        }
        if (this.tenantMenuList?.includes('Sap Exchange')) {
          this.gameProvider.push('sap_lobby')
        }
        if (this.tenantMenuList?.includes('Powerplay Exchange')) {
          this.gameProvider.push('Powerplay')
        }
        if (this.tenantMenuList?.includes('Turbo Stars')) {
          this.gameProvider.push('TurboStars')
        }
      }
      else {
          this.gameProvider = [...this.gameProvider, 'Jetfair','bti_sportsbook','sbs_sportsbook','sap_lobby','Powerplay','TurboStars']
      }
    }
  }
  filterGameProviders(type: any) {
    let gameProvidersToDisplay = this.gameProvider
    if (type == '') {
      return gameProvidersToDisplay
    }
    if (type == 'casino') {
      const providersToRemove = ['bti_sportsbook', 'Jetfair', 'sbs_sportsbook', 'sap_lobby', 'Powerplay','TurboStars'];
      return gameProvidersToDisplay = gameProvidersToDisplay.filter(item => !providersToRemove.includes(item));
    }
    else {
      let ProviderArr:any[] = []

      if(this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0){
        if(this.params.tenantId){
          if (this.tenantMenuList?.includes('Exchange')) {
            ProviderArr.push('Jetfair')
          }
          if (this.tenantMenuList?.includes('BTI SportsBook')) {
            ProviderArr.push('bti_sportsbook')
          }
          if (this.tenantMenuList?.includes('Saba SportsBook')) {
            ProviderArr.push('sbs_sportsbook')
          }
          if (this.tenantMenuList?.includes('Sap Exchange')) {
            ProviderArr.push('sap_lobby')
          }
          if (this.tenantMenuList?.includes('Powerplay Exchange')) {
            ProviderArr.push('Powerplay')
          }
          if (this.tenantMenuList?.includes('Turbo Stars')) {
            ProviderArr.push('TurboStars')
          }
        }
        else {
            ProviderArr = ['Jetfair','bti_sportsbook','sbs_sportsbook','sap_lobby','Powerplay','TurboStars']
        }
      }
        if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Exchange')) {
          ProviderArr.push('Jetfair')
        }
        if (this.adminAuthService?.tenantDataValue?.menuList?.includes('BTI SportsBook')) {
          ProviderArr.push('bti_sportsbook')
        }
        if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Saba SportsBook')) {
          ProviderArr.push('sbs_sportsbook')
        }
        if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Sap Exchange')) {
          ProviderArr.push('sap_lobby')
        }
        if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Powerplay Exchange')) {
          ProviderArr.push('Powerplay')
        }
        if (this.adminAuthService?.tenantDataValue?.menuList?.includes('Turbo Stars')) {
          ProviderArr.push('TurboStars')
        }
      return gameProvidersToDisplay = ProviderArr
    }
  }
  getMakerCheckerData(txn: any, param: any, module: any) {
    let filteredObj = (module === 'maker') ? (txn?.maker_data) : (txn?.checker_data);
    let data
    switch (param) {
      case 'email':
        data = filteredObj.email
        break;
      case 'time_stamp':
        data = this.getFormattedDateTime(filteredObj.time_stamp)
        break;
      case 'user_name':
        data = (filteredObj?.user_name || '-')
        break;
      default: ''
        data = ''
        break;
    }
    return data
  }
  toggleDetails(index: number) {
    this.selectedItemIndex = (this.selectedItemIndex === index) ? null : index;
  }
    modifyKeysBasedOnConditions(attributePermissions:any) {
        for (let attribute in attributePermissions) {
            if (attributePermissions.hasOwnProperty(attribute)) {
                let permissionKey = attributePermissions[attribute];
                if (permissionKey !== null) {
                    if (!this.permissionService.checkPermissionForPlayerKey('players_key',permissionKey)) {
                        delete attributePermissions[attribute];
                    }
                }
            }
        }

        let finalAttributes = Object.keys(attributePermissions);

        return finalAttributes;
    }

  convertToTimeZone(utcDate: any, timeZone: any) {
    const options = { timeZone };
    return utcDate.toLocaleString('en-US', options);
  }

  getFormattedDateTime(date:any){
    if(this.params.time_zone_name == 'UTC +00:00'){
      this.params.time_zone_name = 'Africa/Abidjan'
    }
    const formattedDateTime = this.convertToTimeZone(new Date(date), this.params.time_zone_name);
    return formattedDateTime;
  }
  columnsSelected(column:any){
    if (typeof column === 'string') {
      if (column === 'ALL') {
        this.selectedColumns = ['ALL'];
      } else {
        this.selectedColumns = [column];
      }
    } else if (Array.isArray(column)) {
      this.selectedColumns = column;
    }
  }

  allSelected = false;

  toggleSelection(column: string) {
    let checked= this.isSelected(column);
    if(column=='ALL' && checked){
      this.selectedColumns=[];
    }else if(column=='ALL' && !checked){
      this.selectedColumns=this.allColumns;
    }else{
      if (!checked) {
        this.selectedColumns.push(column);
        this.selectedColumns.length==(this.allColumns.length-1)?this.selectedColumns.push('ALL'):'';
      } else {
        this.selectedColumns.includes('ALL') ? this.selectedColumns = this.selectedColumns.filter(col => col !== 'ALL'):'';
        this.selectedColumns = this.selectedColumns.filter(col => col !== column);
      }
    }

  }

  isSelected(column: string): boolean {
    return this.selectedColumns.includes(column);
  }

  selectAll(checked: boolean) {
    this.selectedColumns = checked ? [...this.allColumns] : [];
    this.allSelected = checked;
  }
  showCheckboxes() {
    var checkboxes = document.getElementById("checkboxes");
    if (checkboxes !== null) {
    if (!this.expanded) {
      checkboxes.style.display = "block";
      this.expanded = true;
    } else {
      checkboxes.style.display = "none";
     this.expanded = false;
    }
  }
}


  /**
   * Translates game codes to human-readable names.
   *
   * This function takes a game code as input and returns a more readable name for the game.
   * It supports a predefined list of games, each with a specific code mapped to a user-friendly name.
   * If the game code does not match any of the predefined ones, it returns the original code.
   *
   * @param {string} game The game code to translate.
   * @return {string} The translated name of the game or the original code if no match is found.
   */
  translateGame(game: string): string {
    const gameNames: { [key: string]: string } = {
      'bti_sportsbook': 'BTI Sportsbook',
      'sbs_sportsbook': 'Saba Sportsbook',
      'sap_lobby': 'Sap Exchange',
      'Powerplay': 'Powerplay',
      'Jetfair': 'Jetfair',
      'TurboStars': 'Turbo Sports Book'
    };

    return gameNames[game] || game;
  }


  copyToClipboard(content: string): void {
    // Using modern clipboard API instead of deprecated execCommand
    navigator.clipboard.writeText(content).then(() => {
      toastr.options = {
        progressBar: true,
        preventDuplicates: true,
        onclick: null,
      };
      toastr.info('Copied');
    }).catch(err => {
      console.error('Could not copy text: ', err);
    });
  }

  getName(name:string): string {
    return name.replace(/_/g, ' ')
  }

  checkSecondBackOfficeDomainExists(_tenantId: any) {
    // Prefix unused parameter with underscore
    this.tenantService.checkSecondBackOfficeDomainExists({ tenantId: this.params.tenantId })
    .subscribe(
      (res: any) => {
       if (res && res.record.sboDomainExists == true && this.superAdminAuthService.superAdminUserValue?.role != 'manager') {
          this.secondBackOfficeDomainExistsForTenant = true;
          setTimeout(function () {
            $('#sb_player_type').val('real_players').trigger('change');
          }, 100);
        }else{
          this.secondBackOfficeDomainExistsForTenant = false;
        }
        ($('#sb_player_type').val(this.params.playerCategory).trigger('change'));
        this.getAllFilters();
      }
    );
  }
  onPlayerTypeChange(event: any) {
    this.params.playerCategory = event.target.value;
    // Call getAllFilters to update filters when playerCategory changes
    this.getAllFilters();
  }

    selectUser(data: any) {
    if(data.value == null) return;
    this.selectedSelect2UserData = data;
    const id = data.value
    this.params = { ...this.params, userId: id };
    // Call getAllFilters to update filters when userId changes
    this.getAllFilters();
  }
  selectedColumnsList(event: any) {
    this.selectedColumns = event;
    this.allColumns = this.allColumns.map((player: any) => {
      player.selected = this.selectedColumns.includes(player.name)
      return player;
    })
  }

  columnPermission() {

    this.allColumns = [
      { name: 'all', permission: true, },
      { name: 'player_id', permission: this.permissionService.PERMISSIONS?.report_attributes?.player_id, },
      { name: 'player_name', permission: this.permissionService.PERMISSIONS?.players_key?.user_name, },
      { name: 'agent_name', permission: this.permissionService.PERMISSIONS?.players_key?.agent_details, },
      { name: 'round_id', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.round_id, },
      { name: 'transaction_id', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.transaction_id, },
      { name: 'type', permission: this.permissionService.PERMISSIONS?.report_attributes?.game_type },
      { name: 'action_type', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.action_type, },
      { name: 'game_type', permission: this.permissionService.PERMISSIONS?.players_key?.total_balance, },
      { name: 'table_id', permission: this.permissionService.PERMISSIONS?.report_attributes?.table_id },
      { name: 'activity_timestamp', permission: this.permissionService.PERMISSIONS?.report_attributes?.activity_timestamp },
      { name: 'initial_balance', permission: this.permissionService.PERMISSIONS?.players_key?.before_balance },
      { name: 'bet_withdraw', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.bet_withdraw },
      { name: 'win_deposit', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.win_deposit },
      { name: 'ending_balance', permission: this.permissionService.PERMISSIONS?.players_key?.after_balance },
      { name: 'revenue', permission: this.permissionService.PERMISSIONS?.transaction_attributes?.revenue },
      { name: 'status', permission: this.permissionService.PERMISSIONS?.report_attributes?.status },
      { name: 'internal_tracking_id', permission: this.permissionService.PERMISSIONS?.report_attributes?.internal_tracking_id }

    ];
       // remove from selectedColumns if permission is not there 
    let columnWithPermission = this.allColumns.filter(column => column.permission);
    this.selectedColumns = this.selectedColumns.filter(column => columnWithPermission.some(col => col.name === column));
    if (this.selectedColumns.length) {
      this.allColumns.forEach(column => {
        if (column.permission) {
          column.selected = this.selectedColumns.includes(column.name)
        } else {
          column.selected = false
        }
      })
    }

    return this.allColumns

  }


  /**
   * Applies the current filters to the player list and hides the filter modal.
   *
   * This method uses the base implementation from BaseFilterComponent
   * but provides a custom callback to get the report data.
   */
  applyFilters(): void {
    super.applyFilters(this.params, this.initialFilters, () => this.submitFilter());
  }


  /**
   * Retrieves and organizes all available filters for the player list component.
   *
   * This method defines a set of filters grouped by categories such as `MAIN_FILTERS`,
   * `ACCOUNT`, `BALANCE_RANGE`, `DATE_RANGE`, `PLAYER`, and `DOCUMENTS`. Each filter
   * includes a key, a group, and a permission condition that determines its availability.
   *
   * The filters are processed using the `permissionService.getFormattedFilters` method,
   * which formats the filters and groups them accordingly. The formatted filters and
   * groups are then assigned to `customFilters` and `filterGroups` respectively.
   *
   * @returns {void} This method does not return a value. It updates the `customFilters`
   * and `filterGroups` properties of the component.
   */
  override getAllFilters(): void {
    const filters = this.getFilters();
   const result = this.filterService.getFormattedFilters(filters, this.filteredKeys);
    this.customFilters = result.customFormat;
    this.filterGroups = result.filterGroups;

         setTimeout(() => {
        this.select2ResetService.initializeMultiSelect(this.params.game_type, 'game_type')
        this.select2ResetService.initializeMultiSelect(this.params.game_provider, 'game_provider')
        this.select2ResetService.initializeMultiSelect(this.params.action_type, 'action_type')
        this.select2ResetService.initializeMultiSelect(this.params.agent_id, 'agent_id')
        if (this.params.userId) {
          this.select2ResetService.setUserSelect2(this.selectedSelect2UserData, 'user');
        }

      },100)
  }


  // Using the base implementation of getFilterCount from BaseFilterComponent
  // which already handles counting changed filters

  // Using the base implementation of allFilters getter from BaseFilterComponent

  // Using the base implementation of isDeepMatchValues from BaseFilterComponent
  // which already handles deep comparison of arrays

  /**
   * Checks if a filter value has changed from its initial value
   *
   * @param key - The filter key to check
   * @returns True if the filter value has changed, false otherwise
   */
  isFilterChanged(key: string): boolean {
    return super.isFilterChanged(key, this.initialFilters, this.params);
  }

  // Using the base implementation of toggleFilterModal from BaseFilterComponent
  // which already handles toggling the showFilterModal property


  /**
     *
   * This method implements the abstract method from BaseFilterComponent.
   * It returns an array of filter configurations.
   *
   * @returns {FilterConfig[]} An array of filter configurations.
   */
//   Unified Transaction Report
// =>Filters : Search,Select User,Round ID,Select agent


  override getFilters(): FilterConfig[] {
    // Reordered filters according to requirements: Search, Select User, Round ID, Select Agent
    return [
      // Top 4 filters as per requirements
      { group: 'TRANSACTION', key: 'transactionId', permission: true, filterKey: 'transactionId' },
      { group: 'PLAYER', key: 'userId', permission: this.permissionService.PERMISSIONS?.players?.R, filterKey: 'userId' },
      { group: 'DATE_RANGE', key: 'time_type', permission: true, filterKey: 'time_type' },
      { group: 'DATE_RANGE', key: 'time_period', permission: this.params.time_type == 'custom', filterKey: 'time_period' },
      { group: 'TRANSACTION', key: 'roundId', permission: true, filterKey: 'roundId' },
      { group: 'GAME', key: 'action_category', permission: true, filterKey: 'action_category' },
      { group: 'GAME', key: 'game_provider', permission: this.params.type != 'financial', filterKey: 'game_provider' },
      { group: 'GAME', key: 'game_type', permission: this.params.game_provider && this.params.game_provider != 'Jetfair', filterKey: 'game_type' },

      // Other filters - moved below the top 4 filters
      { group: 'PLAYER', key: 'player_type', permission: false, filterKey: 'player_type' },
      { group: 'PLAYER', key: 'agent_id', permission: this.permissionService.PERMISSIONS?.players_key?.agent_details && this.permissionService.PERMISSIONS?.agents?.R && this.hideShow, filterKey: 'agent_id' },

      { group: 'PLAYER', key: 'currency', permission: true, filterKey: 'currency' },
      { group: 'TRANSACTION', key: 'type', permission: true, filterKey: 'type' },
      { group: 'TRANSACTION', key: 'action_type', permission: true, filterKey: 'action_type' },
      { group: 'TRANSACTION', key: 'internal_error_code', permission: false, filterKey: 'internal_error_code' },

      { group: 'PLAYER', key: 'playerCategory', permission: (this.isSuperAdmin && this.secondBackOfficeDomainExistsForTenant == true) || (this.permissionService.checkBotDomain() == true), filterKey: 'playerCategory' },
    ];
  }


  filterActionType(type:any = '')
  {
    let checkSuperAdmin = this.superAdminAuthService?.superAdminTokenValue?.length > 0
    checkSuperAdmin = checkSuperAdmin ? false : true
    if (this.superAdminAuthService?.superAdminTokenValue?.length > 0 ? checkSuperAdmin : !(this.adminAuthService?.tenantDataValue?.menuList?.includes('Exchange') || this.adminAuthService?.tenantDataValue?.menuList?.includes('Powerplay Exchange') || this.adminAuthService?.tenantDataValue?.menuList?.includes('Turbo Stars'))) {
      this.transactionTypes = this.transactionTypes.filter(transaction => transaction.category != 'Jetfair')
    }

    if(this.params.type === 'game' && type === ''){
      return this.transactionTypes.filter(transaction => transaction.category === 'st8' || transaction.category === 'Jetfair' || transaction.category === 'Powerplay' || ['bet_one_time_bonus','refund_one_time_bonus'].includes(transaction.title))
    }

    if(this.params.type === 'financial' && type === ''){
      return this.transactionTypes.filter(transaction => transaction.category === this.params.type && transaction.title !== 'bet_one_time_bonus' && transaction.title !== 'refund_one_time_bonus');
    }

    switch (type) {
      case 'casino':
        return this.transactionTypes.filter(transaction => transaction.category === 'st8' || ['bet_one_time_bonus','refund_one_time_bonus'].includes(transaction.title));
      case 'sports':
        return this.transactionTypes.filter(transaction => transaction.category === 'Jetfair' || transaction.category === 'Powerplay' || transaction.category === 'sports');
      default:
        return this.transactionTypes;
    }
  }

   openGameNames(searchID:any){
      let data = {
        debitID : searchID
      }
      if (this.superAdminAuthService.superAdminTokenValue && this.superAdminAuthService.superAdminTokenValue.length > 0) {
        this.ReportService.getSuperAdminMarketGameNames(data).subscribe((res: any) => {
          this.marketNames = res.data.markets;
        });
      }else{
        this.ReportService.getAdminMarketGameNames(data).subscribe((res: any) => {
          this.marketNames = res.data.markets;
        });
      }
    }

  /**
   * Transforms action type values to human-readable format using the standard getTransactionType function
   * @param actionType - The action type value from the API
   * @returns Human-readable action type string
   */
  getActionTypeDisplay(actionType: string): string {
    return getTransactionType(actionType);
  }
}
