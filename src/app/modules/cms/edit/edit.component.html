<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <app-breadcrumb title="Custom Pages" class="custom-main-heading" [breadcrumbs]="breadcrumbs"></app-breadcrumb>
  <!-- /.content-header -->


  <section class="content">
    <div class="container-fluid px-0">

      <div class="row custom-row">
        <!-- left column -->
        <div class="col-md-12">
          <!-- general form elements -->
          <div class="card mb-0">
            <div class="card-header p-0 mb-16">
              <h3 class="card-title">{{'cms_pages' | labelName}}</h3>
            </div>
            <!-- /.card-header -->
            <!-- form start -->
            <form [formGroup]="cmsForm" (ngSubmit)="onSubmit()">
              <div class="card-body">

                <div class="row">
                  <div class="col-md-12"  style="margin-bottom: 0">
                    <app-add-info 
                   [multiLanguage]="true"
                   [languageTabs]="true"
                   [tabSelected]="tabSelected"
                   (onAddInfoChange)="tabChange($event)"
                 ></app-add-info>
                  </div>
                  <div class="col-12 col-md-6">
                    <div class="form-group"  *ngIf="titleList.length">
                   
                      <app-add-info 
                      [addInfo]="titleList"
                      type="input"
                      [submitted]="submitted"
                      [tenantId]="tenant_id"
                      label="Title"
                      [placeholder]="'enter_title' | labelName"
                      [multiLanguage]="true"
                      [languageTabs]="false"
                      [validations]="titleValidations"
                      [tabSelected]="tabSelected"
                      (onAddInfoChange)="contentInfoChange($event, 'title')"
                    ></app-add-info>
                    </div>
                  </div>

                  <div *ngIf="this.f.type.value == 1 && content.length" class="col-md-12 borderline">
                 
                    <app-add-info 
                    [addInfo]="content"
                    type="ckeditor"
                    [submitted]="submitted"
                    [tenantId]="tenant_id"
                    label="Content"
                    placeholder="Enter Content"
                    [multiLanguage]="true"
                    [languageTabs]="false"
                    [validations]="contentValidations"
                    [tabSelected]="tabSelected"
                    (onAddInfoChange)="contentInfoChange($event, 'content')"
                  ></app-add-info>
                  </div>
                 

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label class="position-static" for="slug">Active / Inactive <span class="ml-1 text-danger">*</span></label>
                      <div class="custom-control custom-switch custom-switch-off-danger custom-switch-on-success">
                        <input type="checkbox" class="custom-control-input" formControlName="active" id="customSwitch3">
                        <label class="custom-control-label" for="customSwitch3">You can {{(cms.active ? 'deactivate' : 'active')}} it, if you {{(cms.active ? "don't" : '')}} want to publish.</label>
                      </div>
                      </div>
                  </div>
                  <div class="col-12 col-md-6 col-lg-4 col-xl-3">
                    <div class="form-group">
                      <label for="slug">{{'slug' | labelName}}<span class="ml-1 text-danger">*</span></label>
                      <input type="text" formControlName="slug" (paste)="(false)" (keypress)="removeSpace($event)" class="form-control" id="slug"
                        placeholder="Enter Slug" />
                      <div class="invalid-feedback" *ngIf="submitted && f.slug.errors">
                        <div *ngIf="f.slug.errors.required">{{'slug_is_required' | labelName}}</div>
                      </div>
                    </div>
                    
                  </div> 

                  <div class="col-12 col-md-6">
                    <div class="form-group">
                      <label class="position-static" for="type">{{'type' | labelName}}<span class="ml-1 text-danger">*</span></label>
                      <div>
                        <div class="form-check form-check-inline">
                          <input formControlName="type" class="form-check-input" type="radio" id="cmsTypeInternal" [value]="1">
                          <label class="form-check-label" for="cmsTypeInternal">{{'internal' | labelName}}</label>
                        </div>
                        <div class="form-check form-check-inline">
                          <input formControlName="type" class="form-check-input" type="radio" id="cmsTypeExternal" [value]="2">
                          <label class="form-check-label" for="cmsTypeExternal">{{'external' | labelName}}</label>
                        </div>
                      </div>
                      <div class="invalid-feedback" *ngIf="submitted && f.type.errors">
                        <div *ngIf="f.type.errors.required">{{'type_is_required' | labelName}}</div>
                      </div>
                    </div>
                  </div>   


      

                  <div *ngIf="this.f.type.value == 2" class="col-md-12">
                    <div class="form-group">
                      <label for="link">{{'link' | labelName}}<span class="ml-1 text-danger">*</span></label>
                      <input type="text" formControlName="link" class="form-control" id="link"
                        placeholder="Enter Link" />
                      <div class="invalid-feedback" *ngIf="submitted && f.link.errors">
                        <div *ngIf="f.link.errors.required">{{'link_is_required' | labelName}}</div>
                      </div>
                    </div>
                    
                  </div>


                  <div *ngIf="this.f.type.value && this.f.type.value == 1" class="col-md-12">
                    <hr class="mt-0">
                      <div class="form-group clearfix">
                        <div class="icheck-primary d-inline">
                          <input type="checkbox" id="checkboxPrimary1" (change)="userWarning($event)" formControlName="enable_flag">
                          <label for="checkboxPrimary1">Enable / Disable CMS Page For Registration Page <span class="ml-1 text-danger">*</span></label>
                        </div>
                      </div>
                  </div>
                </div>





              </div>
              <!-- /.card-body -->
              <div class="card-footer custom-card-footer">
                <button type="submit" [disabled]="cmsLoader" class="btn custom-primary-btn">
                  <span *ngIf="cmsLoader" class="spinner-border spinner-border-sm" role="status"
                    aria-hidden="true"></span>{{'submit' | labelName}}</button>
                <a routerLink="/cms" class="btn custom-btn-warning">{{'cancel' | labelName}}</a>
              </div>
            </form>
          </div>
          <!-- /.card -->

        </div>
      </div>
      <!-- /.row -->



    </div><!-- /.container-fluid -->
  </section>


</div>
