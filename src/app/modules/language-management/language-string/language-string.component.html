<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <app-breadcrumb title="{{'language_strings' | labelName}}" [breadcrumbs]="breadcrumbs"
    class="custom-main-heading"></app-breadcrumb>
  <!-- /.content-header -->
  <section class="content">
    <div class="container-fluid px-0">
      <div class="row custom-row">
        <div class="col-md-12">
          <div class="card mb-0">
            <div class="card-header p-0 mb-2">
              <div class="row w-100 mx-0 d-flex align-items-center mainHeadFilter">
                <ng-container *ngIf="
                            superAdminAuthService?.superAdminTokenValue &&
                            superAdminAuthService.superAdminTokenValue.length > 0
                          ">
                  <div class="col-12 col-sm-6 col-lg-4 col-xl-3 selectTenantCol px-0">
                    <div class="form-group">
                      <label for="tenant_id"> {{'select_tenant' | labelName}}</label>
                      <select select2 id="tenant_id" (onSelect)="tenantFilter($event)"
                        [(ngModel)]="languageStringParams.tenant_id" [attr.data-placeholder]="'select' | labelName">
                        <option [value]="0">{{'super_admin' | labelName}}</option>
                        <option *ngFor="let tenant of tenantsList" [value]="tenant.id">
                          {{ tenant.name }}
                        </option>
                      </select>
                    </div>
                  </div>
                </ng-container>


              </div>
            </div>
            <!-- Buttons removed from here -->
            <div class="col-md-12 px-0">
              <div class="card">
                <div class="card-body">
                  <div class="row mx-0 filterRow">
                    <!-- Filters -->
                    <div class="col-xxl-9">
                      <div class="row">
                        <ng-container>
                          <div class="col-md">
                            <div class="form-group" [ngClass]="{
                                'select-form': initialFilters.search_page != languageStringParams.search_page
                              }">
                              <label for="search_page">{{'search_by_page' | labelName}}</label>
                              <select select2 class="form-control" id="search_page"
                                (onSelect)="pageFilter($event, 'page')" [(ngModel)]="languageStringParams.search_page"
                                (ngModelChange)="onPageChange($event)"
                                [attr.data-placeholder]="'select_page' | labelName">
                                <option [value]="''" selected>{{'select_page' | labelName}}</option>
                                <option *ngFor="let option of pageList" [value]="option.value">
                                  {{ option.name }}
                                </option>
                              </select>
                            </div>
                          </div>
                        </ng-container>
                        <div class="col-md">
                          <div class="form-group" [ngClass]="{
                              'select-form': initialFilters.search_translation != languageStringParams.search_translation
                            }">
                            <label for="search_translation">{{'search_by_translation' | labelName}}</label>
                            <input type="text" class="form-control fnt-14" id="search_translation" [(ngModel)]="
                              languageStringParams.search_translation
                            " placeholder="{{'search_translation' | labelName}}" />
                          </div>
                        </div>
                        <div class="col-md" *ngIf="languageStringParams.search_page">
                          <div class="form-group" [ngClass]="{
                              'select-form': initialFilters.languageKeyId != languageStringParams.languageKeyId
                            }">
                            <label for="languageKeyId">{{'search_by_key' | labelName}}</label>
                            <select select2 class="form-control" id="languageKeyId"
                              (onSelect)="pageFilter($event, 'key')" [attr.data-placeholder]="'select_key' | labelName"
                              [(ngModel)]="languageStringParams.languageKeyId"
                              (ngModelChange)="onLanguageKeyChange($event)">
                              <option [value]="''">{{'select_key' | labelName}}</option>
                              <option *ngFor="let option of filteredKeyList" [value]="option.id">
                                {{ option.key }}
                              </option>
                            </select>
                          </div>
                        </div>
                        <div class="col-md"></div>
                      </div>
                    </div>
                    <!-- Reset and Search -->
                    <div class="col-xxl-3 resetSearch">
                      <div class="form-group custom-btn-secondary form-group-head m-0 text-start">
                        <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn"
                          (click)="resetFilter()"> <i class="fa fa-refresh"></i> {{'reset_filter' | labelName}}
                        </button>
                        <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn searchBtn fnt-12"
                          (click)="filterLanguageStrings()"><i class="fa fa-search"></i> {{'search' | labelName}}
                        </button>
                      </div>
                    </div>

                    <div class="col-12 px-0">
                      <div class="row rowBtnWrap justify-content-end mx-0">
                        <!-- buttons -->
                        <div class="d-inline-flex align-items-center flex-grow-1 flex-wrap headFiltersRow">
                          <div class="buttonId">
                            <ng-container
                              *ngIf="this.permissionService.checkPermission('language_strings','export') && !this.permissionService.isManager">
                              <button class="btn btn-primary mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow" type="button" (click)="openCsvModal()">
                                <i class="fas fa-upload"></i> {{'bulk_csv_upload' | labelName}}
                              </button>
                            </ng-container>
                          </div>
                          <div class="buttonId">
                            <a *ngIf="(isSuperAdmin || this.permissionService.checkPermission('language_strings','C')) && !this.permissionService.isManager"
                              class="btn btn-success mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow"
                              [routerLink]="['/languages/string', 0,languageStringParams.tenant_id]">
                              <i class="fas fa-user-plus"></i> {{'add_new' | labelName}}
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12 mt-16 px-0">
                    <div class="table-responsive custom-table hCustom">
                      <table class="table table-bordered">
                        <thead>
                          <tr>
                            <th>{{'id' | labelName}}</th>
                            <th>{{'page' | labelName}}</th>
                            <th>{{'key' | labelName}}</th>
                            <th>{{'english' | labelName}}</th>
                            <th *ngIf="selectedLanguageName">{{ selectedLanguageName }}
                            </th>
                            <th>{{'created_at' | labelName}}</th>
                            <th *ngIf="this.permissionService.checkPermission('language_strings','U') || isSuperAdmin"
                              class="centerText">
                              {{'action' | labelName}}</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr
                            *ngFor="let languageString of languageStringsResult | keyvalue  | paginate: { itemsPerPage: languageStringParams.size, currentPage: langP, totalItems: total, id: 'stringId'}">
                            <td>{{ languageString.key }}</td>
                            <td>{{ getPageName(languageString.value.page)}}</td>
                            <td>{{ languageString.value.key }}</td>
                            <td>{{ languageString.value.languages.English || ('na' | labelName) }}
                            </td>
                            <td *ngIf="selectedLanguageId">{{
                              languageString.value.languages[selectedLanguageName ||
                              ''] || ('na' | labelName)}}</td>
                            <td>{{ languageString.value.created_at | date }}</td>
                            <ng-container
                              *ngIf="(this.permissionService.checkPermission('language_strings','U') || isSuperAdmin) && !this.permissionService.isManager;else showElse">
                              <td class="text-center">
                                <a class="edit-btn-grp"
                                  [routerLink]="['/languages/string', languageString.value.languageStringId,languageStringParams.tenant_id]">
                                  <i class="fas fa-edit"></i>
                                </a>
                              </td>
                            </ng-container>
                            <ng-template #showElse>
                              <td>-</td>
                            </ng-template>

                          </tr>
                          <tr *ngIf="firstTimeApiCall && languageStringsResult && languageStringsResult.length === 0">
                            <td colspan="7" class="tnodata">{{'no_records_found' | labelName}}</td>
                          </tr>
                          <tr *ngIf="!firstTimeApiCall">
                            <td colspan="7" class="tnodata">{{'click_search_to_load_data' | labelName}}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="custom-pagination" *ngIf="total>0">
                      <div class="pagination-item-list">
                        <pagination-controls id="stringId" (pageChange)="languagePageChanged($event)"
                          (pageChange)="langP = $event" style="margin-left: -49px;"></pagination-controls>
                        <select class="form-control" id="size" [(ngModel)]="languageStringParams.size"
                          (change)="filterLanguageStrings()">
                          <option *ngFor="let pageSize of pageSizes" [value]="pageSize.name">{{ pageSize.name }}
                          </option>
                        </select>
                      </div>
                      <div>
                        {{'total_record' | labelName}}: {{ total }}
                      </div>
                    </div>
                  </div>



                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>


<div class="modal fade custom-modal" id="modal-bulk-language-strings" aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"> {{'bulk_csv_upload' | labelName}}</h4>
       
      </div>

      <form [formGroup]="langForm">
        <div class="modal-body">
          <div class="form-group" *ngIf="this.permissionService.isSuperAdmin">
            <label for="all_tenants" class="position-static">{{'upload_for' | labelName}}</label>
            <div>
              <div class="form-check form-check-inline">
                <input formControlName="all_tenants" class="form-check-input" type="radio" id="uploadTargetSuperAdmin"
                  [value]="0" />
                <label class="form-check-label bg-transparent" for="uploadTargetSuperAdmin">{{'super_admin' | labelName}}</label>
              </div>
              <div class="form-check form-check-inline">
                <input formControlName="all_tenants" class="form-check-input" type="radio" id="uploadTargetAllTenants"
                  [value]="1" />
                <label class="form-check-label bg-transparent" for="uploadTargetAllTenants">{{'all_tenants' | labelName}}</label>
              </div>
            </div>
          </div>

          <div class="form-group upload-img-group">
            <label class="upload-icon-label" for="csvFile">{{'csv_file' | labelName}}<span
                class="text-danger">*</span></label>
            <div class="input-group">
              <div class="custom-file">
                <input type="file" #file class="custom-file-input" id="exampleInputFile" formControlName="csvFile"
                  (change)="selectCsv(file.files)" accept=".csv" />
                <label class="custom-file-label" for="exampleInputFile">{{'choose_file' | labelName}}</label>
              </div>
            </div>
            <div class="invalid-feedback" *ngIf="csvSubmitted && form.csvFile.errors">
              <div *ngIf="form.csvFile.errors.required">
                {{'select_csv_file' | labelName}}
              </div>
            </div>

            <div *ngIf="uploadedFileName">
              {{'file_uploaded' | labelName}}: {{ uploadedFileName }}
            </div>
          </div>


          <button class="custom-secondry-btn" (click)="sampleCsv()">
            <i class="fa fa-download"></i> {{'sample_csv' | labelName}}
          </button>
          <h5 class="text-danger mt-3">{{'notes' | labelName}}</h5>
          <ul class="text-danger mt-2 mb-0 pl-25">
            <li>
              Do not modify the values in the PAGE and KEY columns.
            </li>
            <li>
              To add a new language, use the abbreviation of that
              language as the column name.
            </li>
            <li>
              Do not use the same language abbreviation more than once
              as a column header.
            </li>
            <li>{{'no_empty_rows_or_columns_should_be_added' | labelName}}<span class="text-primary ml-2">{{'for_example_enus' | labelName}}</span>
            </li>
          </ul>
        </div>
        <div class="modal-footer">
          <div class="d-flex justify-content-between w-100">
             <button type="submit" [disabled]="languageLoader" class="custom-primary-btn m-0" (click)="submitForm()">
              <span *ngIf="languageLoader" class="spinner-border spinner-border-sm" role="status"></span>
              {{'save_changes' | labelName}}
            </button>
            <button type="button" class="btn custom-primary-btn m-0 mr-2" data-dismiss="modal"
              (click)="closeCsvModal()">
              {{'close' | labelName}}
            </button>
           
          </div>
        </div>
      </form>
    </div>
  </div>
</div>