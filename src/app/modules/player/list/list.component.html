<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <app-breadcrumb [title]="'all_players' | labelName" [breadcrumbs]="breadcrumbs" class="custom-main-heading"></app-breadcrumb>
  <!-- /.content-header -->

  <section class="content">
    <div class="container-fluid p-0">
      <div class="row custom-row">
        <div class="col-md-12">
          <div class="card mb-0">
            <ng-container *ngIf="this.permissionService.isSuperAdmin">
              <div class="card-header p-0 mb-2" *ngIf=" filterGroups && filterGroups.length > 0 ; else loader">
                <div class="row w-100 d-flex align-items-center mainHeadFilter"  >
                  <div class="form-group col-12 col-sm-6 col-lg-3 col-xl-3 selectTenantCol">
                    <label for="tenant_id">{{'select_tenant' | labelName}}</label>
                    <select select2 id="tenant_id" style="width: 100%;" [attr.data-placeholder]="'select' | labelName"
                      (onSelect)="tenantFilter($event)" >
                      <option *ngFor="let tenant of tenantsList" [value]="tenant.id">{{tenant.name}}</option>
                    </select>
                  </div>
                </div>
              </div>
            </ng-container>

            <div class="card-header" *ngIf="showAffiliateToken && affiliate_status">
              <div class="jumbotron w-100 p-3">
                <ng-container *ngIf="firstTimeApiCall else clickOnSearchTag">
                  <ng-container *ngIf="agentAffiliateTokenUtl != '' else adminTemp">
                    <h3 class="invite-link copy-before">{{'player_invite_link' | labelName}}</h3><br>
                    <span class="copy-link">
                      {{agentAffiliateTokenUtl}}
                      <span class="copyButton" title="Click To Copy" (click)="copyMessage(agentAffiliateTokenUtl)"
                        value="click to copy"><i class="fa fa-clone"></i> </span>
                      <small class="small-text">{{'click_to_copy' | labelName}}</small>
                    </span>
                  </ng-container>

                  <ng-template #adminTemp>
                    <h6 class=""><i class="fa fa-info-circle"></i> {{'contact_admin_invite_link' | labelName}}</h6>
                  </ng-template>
                </ng-container>

                <ng-template #clickOnSearchTag>
                  <h6 class="text-red mb-0"><i class="fa fa-info-circle"></i> <span [innerHTML]="'click_search_invite_link' | labelName"></span></h6>
                </ng-template>
              </div>



              <!--<button type="button" class="btn btn-info f-right fnt-12" (click)="exportAsXLSX()">{{'csv' | labelName}}<i class="fa fa-download"></i> </button>-->

            </div>
            <div class="card-body" >
              <div class="row mx-0 filterRow">
                <!-- Filters -->
                <div class="col-xxl-9">
                  <div class="row">
                    <ng-container *ngFor="let filter of filterGroups">
                    <ng-container  *ngIf="customFilters && customFilters[filter] && customFilters[filter].length  > 0 && filter === 'MAIN_FILTERS'"><ng-container *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: true }"></ng-container>
                    </ng-container>
                    </ng-container>
                  </div>
                </div>
                <!-- Reset and Search -->
                <div class="col-xxl-3 resetSearch">
                   <div class="form-group custom-btn-secondary form-group-head m-0 text-start">
                     <ng-container *ngTemplateOutlet="filtersSearch"></ng-container>
                      </div>
                </div>

                <div class="col-12 px-0">
                  <div class="row rowBtnWrap justify-content-end mx-0">
                    <!-- buttons -->
                    <div class="d-inline-flex align-items-center flex-grow-1 flex-wrap headFiltersRow buttonList" id="buttonList">
                      <ng-container *ngTemplateOutlet="buttonActions"></ng-container>
                    </div>

                    <!-- Exports -->
                    <ng-container>
                      <div class="d-flex justify-content-end align-items-center selectExport pr-0 ">
                        <ng-container *ngTemplateOutlet="buttonExport"></ng-container>
                      </div>
                    </ng-container>

                  </div>
                </div>

                <div *ngIf="userNames.size > 0" class="col-12 mt-16 px-0">
                  <!-- <p *ngIf="(players && players.length > 0) && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule)) && (isBulkDepositEnable || isBulkWithdrawEnable || isBulkEditEnable)"
                    class="mr-2 f-right mb-3 mt-1">*Selected Players would be unchecked on any filter change.</p> -->
                  <div class="form-group mb-0">

                    <div class="rectangle-container">
                      <label
                      *ngIf="!this.permissionService.isManager && (players && players.length > 0) && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule)) && ((isBulkDepositEnable && this.permissionService.checkPermission('fund_transfer_deposit','C')) || (isBulkWithdrawEnable && this.permissionService.checkPermission('fund_transfer_withdraw','C')) || (isBulkEditEnable && this.permissionService.PERMISSIONS?.players?.U) || this.permissionService.checkPermission('bulk_assign_bonus','C'))"
                        for="size2">{{'selected_users_note' | labelName}}</label>
                      <div class="selected-users-container">
                        <div *ngFor="let user of userNames | keyvalue" class="user-item">
                          <button (click)="removeplayer(user.value.id)" type="button" class="close" data-dismiss="modal"
                            aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                          </button>
                          {{ user.value.user_name ?? user.value.id }}</div>

                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row px-2 mt-16" *ngIf="!isLoader; else loader">
                <!-- <pagination-controls id="playerpid" (pageChange)="playerPageChanged($event)"
                  (pageChange)="playerP = $event" style="margin-left: -49px;"></pagination-controls>
                Total Record {{playerTotal}} -->

                <div class="table-responsive custom-table hCustom">
                  <table class="table table-bordered mb-0">
                    <thead>
                      <tr>
                        <th
                        *ngIf="!this.permissionService.isManager && (players && players.length > 0) && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule)) && ((isBulkDepositEnable && this.permissionService.checkPermission('fund_transfer_deposit','C')) || (isBulkWithdrawEnable && this.permissionService.checkPermission('fund_transfer_withdraw','C')) || (isBulkEditEnable && this.permissionService.PERMISSIONS?.players?.U) || this.permissionService.checkPermission('bulk_assign_bonus','C'))">
                          <div>
                            <input type="checkbox" name="checkbox" [id]="this.playerP"
                              [checked]="this.selectedUsers.has(this.playerP) && this.selectedUsers.get(this.playerP).length == playerParams.size"
                              (change)="toggleAll($event)">
                          </div>
                        </th>
                        <th >
                          <div>
                            {{'id' | labelName}}<app-ordering-arrow [column]="'id'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('user_name')">
                          <div>
                             {{ 'user_name' | labelName }}
                            <app-ordering-arrow [column]="'user_name'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th>
                        <th  [hidden]="!selectedColumns.includes('affiliate_code')" >
                          <div>
                            <!-- Affiliate Code -->
                            {{ 'affiliate_code' | labelName }}
                          </div>
                        </th>

                        <th [hidden]="!selectedColumns.includes('national_id')">
                          <div>
                            <!-- National ID -->
                            {{ 'national_id' | labelName }}
                          </div>
                        </th>
                        <!-- <th>
                          <div>{{'nick_name' | labelName}}<app-ordering-arrow [column]="'nick_name'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th> -->
                        <th [hidden]="!selectedColumns.includes('email')" >
                          <div>
                            <!-- Email -->
                            {{ 'email' | labelName }}
                            <app-ordering-arrow [column]="'email'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('phone_number')">
                          <div>
                            <!-- Phone Number -->
                            {{ 'phone_number' | labelName }}
                            <app-ordering-arrow [column]="'phone'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('first_name')">
                          <div>
                            <!-- First Name -->
                            {{ 'first_name' | labelName }}
                            <app-ordering-arrow [column]="'first_name'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('last_name')">
                          <div>
                            <!-- Last Name -->
                            {{ 'last_name' | labelName }}
                            <app-ordering-arrow [column]="'last_name'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th>
                        <th  [hidden]="!selectedColumns.includes('agent_name')" >
                          <div>
                            <!-- Agent Name -->
                            {{ 'agent_name' | labelName }}
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('total_balance')" >
                          <div>
                            <!-- Total Balance -->
                            {{ 'total_balance' | labelName }}
                            <app-ordering-arrow [column]="'total_amount'" [params]="playerParams"
                              (changeOrder)="setOrder($event)"></app-ordering-arrow>
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('last_login')">
                          <div>
                            <!-- Last Login -->
                            {{ 'last_login' | labelName }}
                          </div>
                        </th>
                        <th  [hidden]="!selectedColumns.includes('commission')" >
                          <div>
                            <!-- Commission (%)  -->
                            {{ 'commission' | labelName }}
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('vip_level')"  class="centerText">
                          <div>
                            <!-- VIP Level -->
                            {{ 'vip_level' | labelName }}

                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('category_type')"  class="centerText">
                          <div>
                            <!-- Category -->
                            {{ 'category_type' | labelName }}

                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('status')"  class="centerText">
                          <div>
                            <!-- Status -->
                            {{ 'status' | labelName }}
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('demo')">
                          <div>
                            <!-- Demo -->
                            {{ 'demo' | labelName }}
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('kyc_done')" class="centerText">
                          <div>
                            <!-- KYC Done? -->
                            {{ 'kyc_done' | labelName }}
                          </div>
                        </th>

                        <th [hidden]="!selectedColumns.includes('referral_code')">
                          <div>
                            <!-- Referral Code -->
                            {{ 'referral_code' | labelName }}
                          </div>
                        </th>


                        <th [hidden]="!selectedColumns.includes('profile_verified')">
                          <div>
                            <!-- Profile Verified -->
                            {{ 'profile_verified' | labelName }}
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('withdrawal_requests')" class="centerText">
                          <div>
                            <!-- Withdrawal Requests -->
                            {{ 'withdrawal_requests' | labelName }}
                             &nbsp; <i data-toggle="tooltip" title="Withdrawal Request Permissions for Specific Users." class="fas fa-info-circle" data-html="true"></i>
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('created_at')">
                          <div>
                            <!-- Created At -->
                            {{ 'created_at' | labelName }}
                          </div>
                        </th>
                        <th [hidden]="!selectedColumns.includes('registration_type')" class="centerText">
                          <div>
                            <!-- Registration Type -->
                            {{ 'registration_type' | labelName }}
                          </div>
                        </th>
                        <th class="centerText">
                          <div>
                            <!-- Action -->
                            {{ 'action' | labelName }}
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        *ngFor="let user of players | paginate: { itemsPerPage: playerParams.size, currentPage: playerP, totalItems: playerTotal, id: 'playerpid' }">
                        <td
                        *ngIf="!this.permissionService.isManager &&  ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule)) && ((isBulkDepositEnable && this.permissionService.checkPermission('fund_transfer_deposit','C')) || (isBulkWithdrawEnable && this.permissionService.checkPermission('fund_transfer_withdraw','C')) || (isBulkEditEnable && this.permissionService.PERMISSIONS?.players?.U) || this.permissionService.checkPermission('bulk_assign_bonus','C') )">
                          <input type="checkbox" name="checkbox" [id]="user?.user_name"
                            (change)="selectUser(user,$event)"
                            [checked]="selectedUsers.has(this.playerP) && selectedUsers.get(this.playerP).includes(user.id)">
                        </td>
                        <td >{{ user.id }} </td>
                        <td [hidden]="!selectedColumns.includes('user_name')"><a [routerLink]="['/players/details', user.id]" class="text-break">{{ user.user_name}}</a>&nbsp;&nbsp;<span *ngIf="isBot(user)" class="badge bg-dark ms-2">{{'bot' | labelName}}</span>
                        </td>
                        <td [hidden]="!selectedColumns.includes('affiliate_code')">{{ user.affiliated_data || '-' }}</td>
                        <td [hidden]="!selectedColumns.includes('national_id')">{{ user?.national_id || '-' }}</td>

                        <!-- <td>{{ user.nick_name }}</td> -->
                        <td [hidden]="!selectedColumns.includes('email')" class="text-break">{{ user.email || '-' }}</td>
                        <td [hidden]="!selectedColumns.includes('phone_number')">{{ user.phone || '-' }}</td>
                        <td [hidden]="!selectedColumns.includes('first_name')">{{ user.first_name || '-' }}</td>
                        <td [hidden]="!selectedColumns.includes('last_name')">{{ user.last_name || '-' }}</td>
                        <td [hidden]="!selectedColumns.includes('agent_name')">{{ user?.agentname }}</td>
                        <td [hidden]="!selectedColumns.includes('total_balance')">
                          <!-- {{ (( +user.total_amount + (oneTimeBonus && this.permissionService.PERMISSIONS?.one_time_bonus?.R ? +user.one_time_bonus_amount : 0))
                          | amountFormat: user.currency_code: '' : false) || 0.00 }} {{ user.currency_code }} -->
                          <span [innerHTML]="(( +user.total_amount + (oneTimeBonus && this.permissionService.PERMISSIONS?.one_time_bonus?.R ? +user.one_time_bonus_amount : 0)) | amountFormat: user.currency_code: 'symbol' : true) || 0.00"></span>
                        </td>
                        <td [hidden]="!selectedColumns.includes('last_login')" class="ipaddress-td">
                        <popover-content #myPopover [closeOnClickOutside]="true" [hidden]="!user?.latest_ip_address">
                          <p class="text-center "><b>{{ user?.latest_ip_address || '' }}</b></p>
                        </popover-content>
                        <img [popover]="myPopover" class="ipaddress-icon"
                          (click)="getCurrentUserIpAddress(user);"
                          src="../../../../assets/dist/svg/ip-address.svg" alt="">
                      </td>
                        <td [hidden]="!selectedColumns.includes('commission')" > {{
                          user?.value != undefined && user.value != null && user.value != 0 ? user.value + '%' : ('na' | labelName) }}
                        </td>
                        <td [hidden]="!selectedColumns.includes('vip_level')" class="text-center">
                          <span class="vip-badge">
                            {{'vip' | labelName}} {{ user?.vip_level }}
                          </span>
                        </td>
                        <td class="text-center" [hidden]="!selectedColumns.includes('category_type')" >
                          <span class="category-badge">
                            {{ user?.category_type | playerCategory }}
                          </span>
                        </td>
                        <td [hidden]="!selectedColumns.includes('status')" class="text-center"> <span [ngClass]="user.active ? 'active-status':'not-active-status'">{{ user.active ?
                            ('is_active' | labelName) :
                            ('deactive' | labelName)
                            }}</span> </td>
                        <td
                        [hidden]="!selectedColumns.includes('demo')">
                          <span [ngClass]="user.demo ? 'active-status':'not-active-status'">{{ user.demo ? ('yes' | labelName) : ('no' | labelName)
                            }}</span>
                        </td>
                        <td [hidden]="!selectedColumns.includes('kyc_done')" class="text-center"><span [ngClass]="user.kyc_done ? 'active-status':'no-kyc-status'">{{ user.kyc_done ?
                            ('yes' | labelName) :
                            ('no' | labelName) }}</span>
                        </td>
                        <td [hidden]="!selectedColumns.includes('referral_code')">
                          <div class="copy-container">
                            <span class="copy-text">{{ user.referral_code || '-' }}</span>
                            <a *ngIf="user.referral_code && user.referral_code !== '-'"
                               (click)="copyMessage(user.referral_code)"
                               title="Copy to clipboard"
                               class="copy-link">
                              <i class="fa fa-copy copy-icon"></i>
                            </a>
                          </div>
                        </td>
                        <td [hidden]="!selectedColumns.includes('profile_verified')" class="text-center"><span [ngClass]="user.profile_verified ? 'active-status':'not-active-status'">{{ user.profile_verified ?
                            ('yes' | labelName) :
                            ('no' | labelName) }}</span>
                        </td>
                        <td class="text-center" [hidden]="!selectedColumns.includes('withdrawal_requests')">
                          <span [ngClass]="user.enable_withdraw_requests ? 'active-status':'not-active-status'">{{ user.enable_withdraw_requests ?
                            ('yes' | labelName) :
                            ('no' | labelName) }}</span>
                          </td>
                          <td [hidden]="!selectedColumns.includes('created_at')">
                            {{user.created_at || ('na' | labelName)}}
                          </td>
                          <td [hidden]="!selectedColumns.includes('registration_type')" class="text-center">
                            <span *ngIf="user.creation_type == 0">{{'normal' | labelName}}</span>
                            <span *ngIf="user.creation_type == 1">{{'one_click' | labelName}}</span>
                            <span *ngIf="user.creation_type != 0 && user.creation_type != 1">{{ 'na' | labelName }}</span>
                          </td>
                                                  <!-- <td [hidden]="!selectedColumns.includes('action')">
                          <div class="actionWrapper">
                            <span>
                              <i class="fas fa-ellipsis-v"></i>
                            </span>
                            <div class="inneractionWrapper">
                              <ng-container  *ngIf="((this.permissionService.checkPermission('players','login_as_user') && isLoginModuleEnabled)) && user.active && !isSuperAdmin &&  !this.permissionService.isManager">
                                <a class="edit-btn-grp" (click)="loginAsUser(user)"> <i class="fa fa-user-plus" aria-hidden="true"></i>{{'impersonate' | labelName}}</a>
                              </ng-container>
                              <ng-container *ngIf=" this.permissionService.checkPlayerKeyExistsAndHasAllPermissions() && this.permissionService.PERMISSIONS?.players?.U && !this.permissionService.isManager">
                                <a class="edit-btn-grp" [routerLink]="['/players', user.id]"> <i class="fa fa-edit custom-icon"></i>{{'edit' | labelName}}</a>
                              </ng-container>
                              <a class="edit-btn-grp" [routerLink]="['/players/details', user.id]"> <i
                                  class="fa fa-eye"></i>{{'user_details' | labelName}}</a>
                              <a class="edit-btn-grp" (click)="openFinancialModal(user)"> <i class="fa fa-money" aria-hidden="true"></i>{{'financial_details' | labelName}}</a>
                              <ng-container *ngIf="(!this.permissionService.isSuperAdmin) && this.permissionService.PERMISSIONS?.referral_settings?.U && (user?.block_type === 0 || user?.block_type == null); else enableReferral">
                                <a class="edit-btn-grp"  type="button" (click)="disableReferralModal(user)"
                                  > <i data-toggle="tooltip" title="Disable Referral" class="fa fa-lock text-danger"></i>{{'disable_referral' | labelName}}</a>
                              </ng-container>
                              <ng-template #enableReferral>
                                <a class="edit-btn-grp"  *ngIf="this.permissionService.PERMISSIONS?.referral_settings?.U && (user?.block_type === 1 || user?.block_type === 2)" type="button" (click)="enableReferralModal(user)"
                                  > <i data-toggle="tooltip" title="Enable Referral" class="fa fa-unlock text-success"></i>{{'enable_referral' | labelName}}</a>
                              </ng-template>
                            </div>
                          </div>
                        </td> -->
                        <td >
                          <div class="actionWrap" [appActionMenu]="user.id">
                            <span class="action-dots ">
                              <i class="fa fa-ellipsis-v"></i>
                            </span>
                            <div class="actionInline ">

                              <ng-container  *ngIf="((this.permissionService.checkPermission('players','login_as_user') && isLoginModuleEnabled)) && user.active && !isSuperAdmin &&  !this.permissionService.isManager">
                                <a class="edit-btn-grp" (click)="loginAsUser(user)"> <img src="../../../../assets/dist/svg/add-user.svg" alt="user">
                                  <span>{{'impersonate' | labelName}}</span>
                                </a>
                              </ng-container>

                              <ng-container *ngIf=" this.permissionService.checkPlayerKeyExistsAndHasAllPermissions() && this.permissionService.PERMISSIONS?.players?.U && !this.permissionService.isManager">
                                <a class="edit-btn-grp" [routerLink]="['/players', user.id]">
                                  <img src="../../../../assets/dist/svg/edit-user.svg" alt="edit">
                                  <span>{{'edit' | labelName}}</span>
                                </a>
                              </ng-container>
                              <a class="edit-btn-grp" [routerLink]="['/players/details', user.id]">
                                <img src="../../../../assets/dist/svg/visible.svg" alt="eye">
                                <span>{{'details_info' | labelName}}</span>
                                <!-- <i class="fa fa-eye"></i>  -->
                              </a>

                              <a class="edit-btn-grp" (click)="openFinancialModal(user)">
                                <img src="../../../../assets/dist/svg/money.svg" alt="money">
                                <span>{{'financial' | labelName}}</span>                         <!-- <i class="fa fa-money" aria-hidden="true"></i>  -->
                              </a>

                              <ng-container *ngIf="this.permissionService.PERMISSIONS?.players?.T && !this.permissionService.isManager">
                                <ng-container *ngIf="user.active; else activeUser">
                                  <button type="button" (click)="updatePlayerStatus(user, 0)" class="action-close-btn">
                                    <img src="../../../../assets/dist/svg/trash.svg" alt="trash">
                                    <span>{{'deactivate' | labelName}}</span>
                                    <!-- <i class="fa fa-remove"></i>  -->
                                  </button>
                                </ng-container>
                                <ng-template #activeUser>
                                  <button type="button" (click)="[updatePlayerStatus(user, 1), $event.stopPropagation()]"
                                    class="action-close-btn">
                                    <img src="../../../../assets/dist/svg/fi_4391443.svg" alt="restore" data-toggle="tooltip" [title]="user.disabled_remarks">
                                    <span>{{'restore' | labelName}}</span>
                                  </button>
                                </ng-template>
                              </ng-container>
                              <ng-container *ngIf="(!this.permissionService.isSuperAdmin) && this.permissionService.PERMISSIONS?.referral_settings?.U && (user?.block_type === 0 || user?.block_type == null); else enableReferral">
                                <button type="button" (click)="disableReferralModal(user)"
                                  class="edit-btn-grp"> <img  class="text-danger-svg" src="../../../../assets/dist/svg/lock.svg" alt="lock">
                                  <span>{{'disable_referral' | labelName}}</span>
                                </button>
                                </ng-container>
                              <ng-template #enableReferral>
                                <button *ngIf="this.permissionService.PERMISSIONS?.referral_settings?.U && (user?.block_type === 1 || user?.block_type === 2)" type="button" (click)="enableReferralModal(user)"
                                  class="edit-btn-grp">  <img  class="text-success-svg" src="../../../../assets/dist/svg/lock.svg" alt="lock">
                                  <span>{{'enable_referral' | labelName}}</span>
                                </button>
                              </ng-template>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr *ngIf="firstTimeApiCall && players && players.length === 0" colspan="13">
                        <td
                          [attr.colspan]="(superAdminAuthService.superAdminTokenValue && superAdminAuthService.superAdminTokenValue.length > 0) ? 20 : 20"
                          class="tnodata">{{'no_records_found' | labelName}} </td>
                      </tr>
                      <tr *ngIf="!firstTimeApiCall" colspan="13">
                        <td
                          [attr.colspan]="(superAdminAuthService.superAdminTokenValue && superAdminAuthService.superAdminTokenValue.length > 0) ? 20 : 20"
                          class="tnodata"> {{'click_search_to_load_data' | labelName}}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="custom-pagination" *ngIf="playerTotal>0">
                  <div class="pagination-item-list">
                    <pagination-controls id="playerpid" (pageChange)="playerPageChanged($event)"
                      (pageChange)="playerP = $event" style="margin-left: -49px;" [nextLabel]=this.permissionService.PAGINATION.NEXT [previousLabel]=this.permissionService.PAGINATION.PREVIOUS [maxSize] = this.permissionService.PAGINATION.MAX_SIZE>
                    </pagination-controls>
                    <select class="form-control" id="size2" [(ngModel)]="playerParams.size" (change)="filterPlayers()">
                      <option *ngFor="let pageSize of pageSizes" [value]="pageSize.name">{{pageSize.name}}</option>
                    </select>
                  </div>
                  <p class="showPage" appPaginationDisplay
                     [currentPage]="playerP"
                     [itemsPerPage]="playerParams.size"
                     [totalItems]="playerTotal">
                  </p>
                </div>
              </div>
            </div>
          </div>

        </div>

      </div>

    </div>
  </section>

</div>




<div class="modal fade custom-modal" id="modal-bulk-finance" aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"> {{(f.type.value == 1) ? 'Deposit' : 'Withdraw'}} </h4>
      </div>

      <form [formGroup]="financeForm" (ngSubmit)="submitFinanceForm()">
        <div class="modal-body">

          <div class="form-group">
            <label class="position-static " for="wallet_type">{{'wallet_type' | labelName}}<span class="text-danger">*</span></label>
            <!-- <select formControlName="wallet_type" class="form-control" id="wallet_type">
              <option value="" disabled>{{'select_wallet_type' | labelName}}</option>
              <option *ngFor="let type of walletTypes" [value]="type.value">{{type.label}}</option>
            </select> -->
            <div>
              <div class="form-check form-check-inline">
                <input formControlName="wallet_type" class="form-check-input" type="radio" id="financeWalletCash"
                  [value]="1">
                <label class="form-check-label bg-transparent " for="financeWalletCash">{{'cash' | labelName}}</label>
              </div>
              <div class="form-check form-check-inline" *ngIf="!(isAgent && marinanTenant)">
                <input formControlName="wallet_type" class="form-check-input" type="radio" id="financeWalletNonCash"
                  [value]="2">
                <label class="form-check-label bg-transparent " for="financeWalletNonCash">{{'non_cash' | labelName}}</label>
              </div>
            </div>
            <div class="invalid-feedback" *ngIf="submitted && f.wallet_type.errors">
              <div *ngIf="f.wallet_type.errors.required">{{'wallet_type_is_required' | labelName}}</div>
            </div>
          </div>

          <div *ngIf="f.type.value == 1 && f.wallet_type.value == 1 && financeForm.contains('source_wallete_id')"
            class="form-group">
            <div class="form-group">
              <label for="source_wallete_id">{{'source_currency_id' | labelName}} <span
                  class="text-danger">*</span></label>
              <select formControlName="source_wallete_id" (change)="selectAdminWallete($event)" class="form-control"
                id="source_wallete_id">
                <option value="" disabled> {{'select_currency' | labelName}} </option>
                <option *ngFor="let currency of currencies" [value]="currency.id">{{currency.currency_name}}</option>
              </select>
              <div class="invalid-feedback" *ngIf="submitted && f.source_wallete_id.errors">
                <div *ngIf="f.source_wallete_id.errors.required"><span>{{'source' | labelName}}</span>{{'currency_id_is_required' | labelName}}</div>
              </div>
            </div>
          </div>

          <div *ngIf="f.type.value == 2 && f.wallet_type.value == 1 && financeForm.contains('target_wallete_id')"
            class="form-group">
            <div class="form-group">
              <label for="target_wallete_id">{{'target_currency_id' | labelName}} <span
                  class="text-danger">*</span></label>
              <select formControlName="target_wallete_id" class="form-control" id="target_wallete_id">
                <option value="" disabled> {{'select_currency' | labelName}} </option>
                <option *ngFor="let currency of currencies" [value]="currency.id">{{currency.currency_name}}</option>
              </select>
              <div class="invalid-feedback" *ngIf="submitted && f.target_wallete_id.errors">
                <div *ngIf="f.target_wallete_id.errors.required"><span>{{'source' | labelName}}</span>{{'currency_id_is_required' | labelName}}</div>
              </div>
            </div>
          </div>

          <div class="form-group mb-0">
            <label for="amount">{{'amount' | labelName}} <span class="text-danger">*</span> <span
                *ngIf="f.type.value == 1 && f.wallet_type.value == 1 && targetWallete && targetWallete.amount"
                class="text-success"> Transfer From (
                {{ targetWallete.amount }} )</span> </label>
            <input digitOnly type="text" formControlName="amount" class="form-control" maxlength="15" id="amount"
              placeholder="{{'enter_amount' | labelName}}" />
            <div class="invalid-feedback" *ngIf="submitted && f.amount.errors">
              <div *ngIf="f.amount.errors.required">{{'amount_rate_required' | labelName}}</div>
              <div *ngIf="f.amount.errors.min">{{'amount_min_error' | labelName}}</div>
              <div *ngIf="f.amount.errors.invalidAmount">{{'balance_not_enough' | labelName}}</div>

            </div>
          </div>

        </div>
        <div class="modal-footer justify-content-between">
          <button type="submit" class="btn custom-secondry-btn " [disabled]="submitted">{{'save_changes_btn' | labelName}}</button>
          <button type="button" class="btn custom-primary-btn" data-dismiss="modal" (click)="closeModal()">{{'close' | labelName}}</button>
        </div>

      </form>

    </div>
  </div>
</div>

<div class="modal fade custom-modal" id="modal-bulk-edit" aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"> {{'edit_title' | labelName}} </h4>
      </div>

      <form [formGroup]="editForm" (ngSubmit)="submitEditForm()">
        <div class="modal-body">

          <div class="form-group" *ngIf="size">
            <label class="position-static " for="status">{{'status' | labelName}}</label>
            <div>
              <div class="form-check form-check-inline">
                <input formControlName="status" (change)="handleChange($event)" class="form-check-input" type="radio"
                  id="bulkEditStatusActive" [value]="true">
                <label class="form-check-label bg-transparent " for="bulkEditStatusActive">{{'is_active' | labelName}}</label>
              </div>
              <div class="form-check form-check-inline">
                <input formControlName="status" (change)="handleChange($event)" class="form-check-input" type="radio"
                  id="bulkEditStatusInactive" [value]="false">
                <label class="form-check-label bg-transparent" for="bulkEditStatusInactive">{{'inactive' | labelName}}</label>
              </div>
            </div>
            <div class="invalid-feedback" *ngIf="submitted && e.status.errors">
              <div *ngIf="e.status.errors.required">{{'status_required' | labelName}}</div>
            </div>
          </div>
          <ng-container *ngIf="permissionService.checkPermission('player_commission_setting','U')">
            <div class="form-group">
              <label for="player_commission">{{'player_commission' | labelName}}</label>
              <input formControlName="player_commission" class="form-control" type="text" id="player_commission"
                (input)="checkFormStatus()" [placeholder]="'player_commission' | labelName">
              <div class="invalid-feedback" *ngIf="submitted && editForm.controls.player_commission.errors">
                <div *ngIf="editForm.controls.player_commission.errors?.min">{{'commission_min_error' | labelName}}</div>
                <div *ngIf="editForm.controls.player_commission.errors?.max">{{'commission_max_error' | labelName}}
                  {{ playerCommissionMaxPercentage ?? 100 }}</div>
              </div>
            </div>
          </ng-container>
          <div class="form-group mb-0">
            <label for="vip_level">{{'vip_level' | labelName}}</label>
            <select class="form-control" select2 (onSelect)="selectVipLevel($event)" id="vip_level"
              [attr.data-placeholder]="'select' | labelName" style="width: 100%;">
              <option value="">{{'select' | labelName}}</option>
              <option *ngFor="let item of getNumArr(11); let i = index"> {{ i }} </option>
            </select>

            <div class="invalid-feedback" *ngIf="submitted && e.vip_level.errors">
              <div *ngIf="e.vip_level.errors.required">{{'vip_level_required' | labelName}}</div>
            </div>
          </div>


          <!-- <div class="form-group">
            <label for="amount">{{'amount' | labelName}}<span class="text-danger">*</span> <span
                *ngIf="f.type.value == 1 && f.wallet_type.value == 1 && targetWallete && targetWallete.amount" class="text-success"> Transfer From (
                {{ targetWallete.amount }} )</span> </label>
            <input digitOnly type="text" formControlName="amount" class="form-control" maxlength="15" id="amount"
              [placeholder]="'enter_amount' | labelName" />
            <div class="invalid-feedback" *ngIf="submitted && f.amount.errors">
              <div *ngIf="f.amount.errors.required">{{'amount_rate_is_required' | labelName}}</div>
              <div *ngIf="f.amount.errors.min">{{'amount_cannot_be_less_than_1' | labelName}}</div>
              <div *ngIf="f.amount.errors.invalidAmount">{{'balance_not_enough' | labelName}}</div>

            </div>
          </div> -->

        </div>
        <div class="modal-footer justify-content-between edit-footer-modal">
          <div class="text-left m-0 closeBtn">
            <button type="button" class="custom-primary-btn" data-dismiss="modal" (click)="closeModal()">{{'close' | labelName}}</button>
          </div>
          <div class="text-right changesWrap m-0">
            <button [disabled]="submitDisable" type="button" class="btn custom-primary-btn" (click)="clearModalForm()">
              {{'clear_changes' | labelName}}</button>
              <ng-container *ngIf="size; else bulkEdit">
                <button [disabled]="submitted" type="submit" class="btn custom-secondry-btn">{{'save_changes_btn' | labelName}}</button>
              </ng-container>
              <ng-template #bulkEdit>
                <ng-container>
                  <button [disabled]="submitDisable" type="button" (click)="confirmBulkEdit()" class="btn custom-secondry-btn">{{'save_changes_btn' | labelName}}</button>
                </ng-container>
              </ng-template>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>


<div class="modal fade custom-modal" id="modal-bulk-csv-finance" aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"> {{'bulk_finance_csv' | labelName}}</h4>
      </div>

      <form [formGroup]="csvFinanceForm">
        <div class="modal-body">

          <div class="form-group">
            <label class="position-static " for="type"> {{'type' | labelName}} <span class="text-danger">*</span></label>
            <div>
              <div *ngIf="this.permissionService.checkPermission('fund_transfer_deposit','C')"
                class="form-check form-check-inline">
                <input formControlName="type" class="form-check-input" type="radio" id="csvTransferTypeDeposit" [value]="1">
                <label class="form-check-label bg-transparent " for="csvTransferTypeDeposit">{{'deposit' | labelName}}</label>
              </div>
              <div *ngIf="this.permissionService.checkPermission('fund_transfer_withdraw','C')"
                class="form-check form-check-inline">
                <input formControlName="type" class="form-check-input " type="radio" id="csvTransferTypeWithdraw" [value]="2">
                <label class="form-check-label bg-transparent " for="csvTransferTypeWithdraw">{{'withdraw' | labelName}}</label>
              </div>
            </div>
            <div class="invalid-feedback" *ngIf="csvSubmitted && csvForm.type.errors">
              <div *ngIf="csvForm.type.errors.required">{{'type_required' | labelName}}</div>
            </div>
          </div>

          <div class="form-group">
            <label class="position-static " for="wallet_type">{{'wallet_type' | labelName}} <span class="text-danger">*</span></label>
            <!-- <select formControlName="wallet_type" class="form-control" id="wallet_type">
              <option value="" disabled>{{'select_wallet_type' | labelName}}</option>
              <option *ngFor="let type of walletTypes" [value]="type.value">{{type.label}}</option>
            </select> -->
            <div>
              <div class="form-check form-check-inline">
                <input formControlName="wallet_type" class="form-check-input" type="radio" id="csvWalletTypeCash"
                  [value]="1">
                <label class="form-check-label bg-transparent " for="csvWalletTypeCash">{{'cash' | labelName}}</label>
              </div>
              <div class="form-check form-check-inline">
                <input formControlName="wallet_type" class="form-check-input" type="radio" id="csvWalletTypeNonCash"
                  [value]="2">
                <label class="form-check-label bg-transparent " for="csvWalletTypeNonCash">{{'non_cash' | labelName}}</label>
              </div>
            </div>
            <div class="invalid-feedback" *ngIf="csvSubmitted && csvForm.wallet_type.errors">
              <div *ngIf="csvForm.wallet_type.errors.required">{{'wallet_type_required' | labelName}}</div>
            </div>
          </div>

          <div
            *ngIf="csvForm.type.value == 1 && csvForm.wallet_type.value == 1 && csvFinanceForm.contains('source_wallete_id')"
            class="form-group">
            <div class="form-group">
              <label for="source_wallete_id">{{'source_currency_id' | labelName}} <span
                  class="text-danger">*</span></label>
              <select formControlName="source_wallete_id" (change)="selectAdminWallete($event)" class="form-control"
                id="source_wallete_id">
                <option value="" disabled> {{'select_currency' | labelName}} </option>
                <option *ngFor="let currency of currencies" [value]="currency.id">{{currency.currency_name}}</option>
              </select>
              <div class="invalid-feedback" *ngIf="csvSubmitted && csvForm.source_wallete_id.errors">
                <div *ngIf="csvForm.source_wallete_id.errors.required"><span>{{'source' | labelName}}</span>{{'currency_id_is_required' | labelName}}</div>
              </div>
            </div>
          </div>

          <div
            *ngIf="csvForm.type.value == 2 && csvForm.wallet_type.value == 1 && csvFinanceForm.contains('target_wallete_id')"
            class="form-group">
            <div class="form-group">
              <label for="target_wallete_id">{{'target_currency_id' | labelName}} <span
                  class="text-danger">*</span></label>
              <select formControlName="target_wallete_id" class="form-control" id="target_wallete_id">
                <option value="" disabled> {{'select_currency' | labelName}} </option>
                <option *ngFor="let currency of currencies" [value]="currency.id">{{currency.currency_name}}</option>
              </select>
              <div class="invalid-feedback" *ngIf="csvSubmitted && csvForm.target_wallete_id.errors">
                <div *ngIf="csvForm.target_wallete_id.errors.required"><span>{{'target' | labelName}}</span>{{'currency_id_is_required' | labelName}}</div>
              </div>
            </div>
          </div>

          <div class="form-group upload-img-group">
            <label class="upload-icon-label" for="csvFile">{{'csv_file' | labelName}}<span class="text-danger">*</span></label>
            <div class="input-group">
              <div class="custom-file">
                <input type="file" #file class="custom-file-input" id="exampleInputFile" formControlName="csvFile"
                  (change)="selectCsv(file.files)" accept=".csv">
                <label class="custom-file-label" for="exampleInputFile">{{'choose_file' | labelName}}</label>
              </div>
            </div>
            <div class="invalid-feedback" *ngIf="csvSubmitted && csvForm.csvFile.errors">
              <div *ngIf="csvForm.csvFile.errors.required">{{'select_csv_file' | labelName}}</div>
            </div>

            <div *ngIf="uploadedFileName">
              {{'file_uploaded' | labelName}} {{ uploadedFileName }}
            </div>
          </div>

          <button class="custom-secondry-btn" (click)="sampleCsv()"><i class="fa fa-download"></i> {{'sample_csv' | labelName}}</button>

        </div>
        <div class="modal-footer justify-content-between">
          <button type="submit" class="custom-secondry-btn"  [disabled]="csvSubmitted" (click)="submitCsvFinanceForm()">
            {{'save_changes_btn' | labelName}} </button>
          <button type="button" class="btn custom-primary-btn" data-dismiss="modal" (click)="closeCsvModal()">{{'close' | labelName}}</button>
        </div>

      </form>

    </div>
  </div>
</div>

<div class="modal fade custom-modal" id="modal-bulk-bonus" aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"> {{'assign_bonus' | labelName}} </h4>
      </div>

      <form [formGroup]="bonusForm" (ngSubmit)="submitBonusForm()">
        <div class="modal-body">
          <span class="modal-note"><strong>{{'bonus_note' | labelName}}</strong> {{'bonus_note_strong' | labelName}}</span>
          <ul class="modal-note">
            <li>{{'bonus_note_item1' | labelName}}</li>
            <li>{{'bonus_note_item2' | labelName}}</li>
            <li>{{'bonus_note_item3' | labelName}}</li>
            <li>{{'bonus_note_item4' | labelName}}</li>
          </ul>

          <div class="form-group">
            <label class="position-static " for="bonus_type">{{'bonus_type' | labelName}}<span
              class="text-danger">*</span></label><br>
              <div class="form-check form-check-inline">
                <input formControlName="bonus_type" class="form-check-input" type="radio" id="bulkBonusTypeDeposit" [value]="1">
                <label class="form-check-label bg-transparent" for="bulkBonusTypeDeposit">{{'deposit' | labelName}}</label>
              </div>
              <div class="form-check form-check-inline">
                <input formControlName="bonus_type" class="form-check-input" type="radio" id="bulkBonusTypeCashback" [value]="2">
                <label class="form-check-label bg-transparent" for="bulkBonusTypeCashback">{{'cashback' | labelName}}</label>
              </div>
            <div class="invalid-feedback" *ngIf="submitted && bonusFormControls.bonus_type.errors">
              <div *ngIf="bonusFormControls.bonus_type.errors.required">{{'bonus_type_required' | labelName}}</div>
            </div>
          </div>
          <div class="form-group mb-0">
            <label for="bonus_id">{{'bonus_list' | labelName}} <span
              class="text-danger">*</span></label>
            <select class="form-control" select2 (onSelect)="selectBonus($event)" id="bonus_id" [attr.data-placeholder]="'select_bonus' | labelName" style="width: 100%;">
              <option [value]="null">{{'select_bonus' | labelName}}</option>
              <ng-container *ngIf="bonusFormControls.bonus_type.value == 1">
                <option *ngFor="let bonus of depositBonuses" [value]="bonus.id"> {{ languageText(bonus.promotion_title) }} ({{bonus.code}}) </option>
              </ng-container>
              <ng-container *ngIf="bonusFormControls.bonus_type.value == 2">
                <option *ngFor="let bonus of cashbackBonuses" [value]="bonus.id"> {{ languageText(bonus.promotion_title) }} ({{bonus.code}}) </option>
              </ng-container>
            </select>

            <div class="invalid-feedback" *ngIf="submitted && bonusFormControls.bonus_id.errors">
                <div *ngIf="bonusFormControls.bonus_id.errors.required">{{'bonus_required' | labelName}}</div>
            </div>
        </div>

        </div>
        <div class="modal-footer justify-content-between">
          <div class="text-left">
            <button type="submit" class="btn custom-secondry-btn" [disabled]="submitted"> {{'save_changes_btn' | labelName}} </button>
          </div>
          <div class="text-right">
            <button type="button" class="btn custom-primary-btn" data-dismiss="modal" (click)="closeBonusModal()">{{'close' | labelName}}</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<div class="modal fade custom-modal" id="modal-financial-data" aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"> {{'financial_data' | labelName}} <small>{{'till_now' | labelName}}</small></h4>
        <button type="button" class="close edit-btn-grp" data-dismiss="modal" aria-label="Close" (click)="closeFinancialModal()">
          <i class="fa fa-times" aria-hidden="true"></i>
        </button>
      </div>

      <div class="modal-body">
        <div *ngIf="!financialModalLoader; else loader">
          <div class="table-responsive custom-table border-b-0">
            <table class="table table-bordered ">
              <thead>
                <tr>
                  <th><div>Total Cash Deposit</div></th>
                  <th><div>Total Non Cash Deposit</div></th>
                  <th><div>Total Deposit</div></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="this.permissionService.PERMISSIONS?.financial_attributes?.total_deposit">
                  <td>{{ getTotalFinancialData?.totalCashDeposit ? (getTotalFinancialData?.totalCashDeposit | number : '1.0-2') + ' ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') : '0.00 ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') }}</td>
                  <td>{{ getTotalFinancialData?.totalNoncashDeposit ? (getTotalFinancialData?.totalNoncashDeposit | number : '1.0-2') + ' ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') : '0.00 ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') }}</td>
                  <td><strong>{{ getTotalFinancialData?.totalDeposit ? (getTotalFinancialData?.totalDeposit | number : '1.0-2') + ' ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') : '0.00 ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') }}</strong></td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="table-responsive custom-table border-b-0 mt-3">
            <table class="table table-bordered ">
              <thead>
                <tr>
                  <th><div>Total Cash Withdrawal</div></th>
                  <th><div>Total Non Cash Withdrawal</div></th>
                  <th><div>Total Withdrawal</div></th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ getTotalFinancialData?.totalCashWithdraw ? (getTotalFinancialData?.totalCashWithdraw | number : '1.0-2') + ' ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') : '0.00 ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') }}</td>
                  <td>{{ getTotalFinancialData?.totalNoncashWithdraw ? (getTotalFinancialData?.totalNoncashWithdraw | number : '1.0-2') + ' ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') : '0.00 ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') }}</td>
                  <td><strong>{{ getTotalFinancialData?.totalWithdraw ? (getTotalFinancialData?.totalWithdraw |number : '1.0-2') + ' ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') : '0.00 ' + (getTotalFinancialData?.currency_code || selectedUser?.currency_code || '') }}</strong></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="modal fade custom-modal" id="modal-bulk-category" aria-hidden="true" style="display: none;">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title"> {{'assign_category' | labelName}} </h4>
      </div>

      <form [formGroup]="categoryForm" (ngSubmit)="submitCategoryForm()">
        <div class="modal-body">

          <div class="form-group">
            <label for="category_id">{{'category_list' | labelName}} <span
              class="text-danger">*</span></label>
            <select class="form-control" select2 (onSelect)="selectCategory($event)" id="category_id" [attr.data-placeholder]="'select_category' | labelName" style="width: 100%;">
              <option [value]="null">{{'select_category' | labelName}}</option>
              <option [value]="1">A</option>
              <option [value]="2">B</option>
              <option [value]="3">C</option>
              <option [value]="4">D</option>
            </select>

            <div class="invalid-feedback" *ngIf="submitted && categoryFormControls.category_type?.errors">
                <div *ngIf="categoryFormControls.category_type.errors?.required">{{'category_required' | labelName}}</div>
            </div>
        </div>

        </div>
        <div class="modal-footer justify-content-between">
          <div class="text-left">
            <button type="submit" class="btn custom-secondry-btn" [disabled]="submitted"> {{'save_changes_btn' | labelName}} </button>
          </div>
          <div class="text-right">
            <button type="button" class="btn custom-primary-btn" data-dismiss="modal" (click)="closeCategoryModal()">{{'close' | labelName}}</button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
<!-- /Mark BOT Popup -->
<div class="modal fade custom-modal"  id="mark-as-bot" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">{{'mark_as_bot' | labelName}}</h4>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="terms_cond" class="position-static">
            {{'terms_and_condition' | labelName}} <span class="text-danger">*</span>
          </label>
          <div>
            <ol style="font-size: 12px; padding-left: 20px;">
              <li>
                {{'terms_note_item1' | labelName}}
              </li>
              <li>{{'terms_note_item2' | labelName}}</li>
              <li>{{'terms_note_item3' | labelName}}</li>
            </ol>
            <div class="form-check form-check-inline">
              <input  [(ngModel)]="isTermsChecked" class="form-check-input" type="checkbox" id="terms_cond" (change)="onTermsChange($event)">
              <label class="form-check-label bg-transparent" for="terms_cond">{{'agree_terms' | labelName}} <span class="text-danger">*</span></label>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer justify-content-between">
        <button
          type="submit"
          class="btn custom-secondry-btn" [disabled]="!isTermsChecked && submitted" (click)="saveChanges()">
          {{'save_changes_btn' | labelName}}
        </button>
        <button
          type="button"
          class="custom-primary-btn"
          data-dismiss="modal"
          (click)="closeBotModal()"
        >
          {{'close' | labelName}}
        </button>
      </div>
    </div>
  </div>
</div>
<!--Mark as BOT Failed Users Popup-->
<!-- Modal -->
<div class="modal fade custom-modal" id="mark-as-bot-failed" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{'mark_as_bot_failed' | labelName}}</h5>
      </div>
      <div class="modal-body">
        <p>{{'bot_failed_message' | labelName}}</p>
        <table class="table table-striped">
          <thead>
            <tr>
              <th>{{'user_id' | labelName}}</th>
              <th>{{'name' | labelName}}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of failedUsers">
              <td>{{ user.id }}</td>
              <td>{{ user.name }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="modal-footer d-flex justify-content-end">
        <button type="button" class="btn custom-secondry-btn" data-bs-dismiss="modal" class="custom-primary-btn" (click)="closeBotModalFailed()">{{'close' | labelName}}</button>
      </div>
    </div>
  </div>
</div>


<ng-template #loader>

  <div class="w-full text-center custom-loader-div d-flex justify-content-center ">
    <!-- <img class="chart-loader" src="./assets/dist/gif/loader.gif" /> -->
    <div class="custom-loader"></div>
  </div>

</ng-template>



<ng-template #buttonActions>
  <ng-container *ngTemplateOutlet="buttonList; context: { buttonType: true }"></ng-container>
  <div class="buttonId showBtn">

    <a class="btn btn-warning mb-2 mb-sm-0 btn-sm custom-primary-btn showMore" type="button" data-toggle="dropdown"
      href="#">
      {{'show_more' | labelName}}
    </a>
    <div class="dropdown-menu dropdown-menu-right" id="showMore">
      <div class="dropdown-item">
        <ng-container *ngTemplateOutlet="buttonList; context: { buttonType: false }"></ng-container>

      </div>
    </div>
  </div>

</ng-template>

<ng-template #buttonExport>
  <ng-container *ngIf="this.permissionService.checkPermission('players', 'export')">
    <div>
      <button type="button" class="btn btn-info f-right fnt-12 custom-secondry-btn" [disabled]="submitted"
        (click)="exportAsXLSX()">
        <i class="fa fa-download"></i> {{'export_as_csv' | labelName}}
      </button>
    </div>
  </ng-container>
  <app-select-all [allColumns]="allColumns" (selectedColumnsChange)="selectedColumnsList($event)">
  </app-select-all>
</ng-template>

<ng-template #filtersSearch>
  <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn" (click)="playerResetFilter()"> <i
      class="fa fa-refresh"></i> {{'reset_filter' | labelName}}
  </button>
  <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn searchBtn fnt-12"
    (click)="filterPlayers()"><i class="fa fa-search"></i> {{'search' | labelName}}
  </button>
</ng-template>


<!-- ALL FILTERS USED IN DASHBOARD  -->
<ng-template #reusableFilterBlock let-filterKey="filterKey" let-mainFilter="mainFilter">

  <ng-container *ngFor="let item of customFilters && customFilters[filterKey] ? customFilters[filterKey] : []">
    <switch [ngSwitch]="item" [ngClass]="{ 'col-md ': mainFilter }">


      <ng-container *ngSwitchCase="'phone_verified'">
        <div class="form-group"  [ngClass]="{ 'select-form ': initialFilters[item]  != playerParams[item] }">
          <div>
            <label for="phone_verified">{{'phone_verified' | labelName}}</label>
            <select class="form-control" id="phone_verified" [(ngModel)]="playerParams.phone_verified">
              <option value="">{{'all' | labelName}}</option>
              <option value="true">{{'yes' | labelName}}</option>
              <option value="false">{{'no' | labelName}}</option>
            </select>
          </div>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="'email_verified'" >

        <div class="form-group " [ngClass]="{ 'select-form ': initialFilters[item]  != playerParams[item] }">
          <!-- <div> -->
          <label for="email_verified">{{'email_verified' | labelName}}</label>
          <select class="form-control" id="email_verified" [(ngModel)]="playerParams.email_verified">
            <option value="">{{'all' | labelName}}</option>
            <option value="true">{{'yes' | labelName}}</option>
            <option value="false">{{'no' | labelName}}</option>
          </select>
          <!-- </div> -->
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="'players_commission'">
        <div class="form-group"  [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <ng-container>
            <label for="players_commission">{{'player_commission' | labelName}}</label>
            <select class="form-control" id="players_commission" [(ngModel)]="playerParams.players_commission">
              <option value="">{{'all' | labelName}}</option>
              <option value="true">{{'yes' | labelName}}</option>
              <option value="false">{{'no' | labelName}}</option>
            </select>
          </ng-container>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'vip_levels'">
        <div class="form-group multiple-selection-group" [ngClass]="{ 'select-form ': isDeepMatchValues(initialFilters[item], playerParams[item]) }">
          <div>
            <label for="vip_levels">{{'vip_levels' | labelName}}</label>
            <select class="form-control " select2 (onSelect)="selectVipLevelSearch($event)" multiple
              [id]="'vip_levels'" [attr.data-placeholder]="'select' | labelName" style="width: 100%;" >
              <option *ngFor="let vipLevel of getNumArr(11); let i = index" [value]="i">VIP {{i}}</option>
            </select>
          </div>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'player_category'">
        <div class="form-group  multiple-selection-group" [ngClass]="{ 'select-form ': isDeepMatchValues(initialFilters[item], playerParams[item])}" >
          <div>
            <label for="player_category">{{'category' | labelName}}</label>
            <select class="form-control" select2 (onSelect)="selectPlayerCategorySearch($event)" multiple
              id="player_category" [attr.data-placeholder]="'select' | labelName" style="width: 100%;">
              <option [value]="null">{{'select' | labelName}}</option>
              <option [value]="1">A</option>
              <option [value]="2">B</option>
              <option [value]="3">C</option>
              <option [value]="4">D</option>
            </select>
          </div>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'min_amount'">
        <div class="form-group custom-balance-range"  [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <div>
            <label for="balanceRange1">{{'minimum_balance' | labelName}}</label>
            <div class="input-group">
              <input #minInput (input)="validateMinInput($event)" id="balanceRange1" type="number" [ngModel]="playerParams.min_amount"
                pattern="^\d+(\.\d{1,2})?$" maxlength="10" class="form-control" [placeholder]="'enter' | labelName">

            </div>
          </div>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'max_amount'">
        <div class="form-group custom-balance-range" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <div>
            <label for="balanceRange2"> {{'maximum_balance' | labelName}} </label>
            <div class="input-group" >
              <input #maxInput (input)="validateMaxInput($event)" id="balanceRange2" type="number" [(ngModel)]="playerParams.max_amount"
                pattern="^\d+(\.\d{1,2})?$" maxlength="10" class="form-control" [placeholder]="'enter' | labelName">
            </div>
          </div>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'kyc_done'">
        <div class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <div>
            <label for="kyc_done">{{'kyc_completed' | labelName}}</label>
            <select class="form-control" id="kyc_done" [(ngModel)]="playerParams.kyc_done">
              <option value="">{{'all' | labelName}}</option>
              <option value="true">{{'yes' | labelName}}</option>
              <option value="false">{{'no' | labelName}}</option>
            </select>
          </div>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'kyc_docs'" >
        <div class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <div>
            <label for="kyc_docs">{{'kyc_documents' | labelName}}</label>
            <select class="form-control" id="kyc_docs" [(ngModel)]="playerParams.kyc_docs">
              <option value="">{{'all' | labelName}}</option>
              <option value="uploaded">{{'uploaded' | labelName}}</option>
              <option value="not_uploaded">{{'not_uploaded' | labelName}}</option>
            </select>
          </div>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'profile_activate'">
        <div class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <div>
            <label for="profile_activate">{{'profile_activate' | labelName}}</label>
            <select class="form-control" id="profile_activate" [(ngModel)]="playerParams.profile_activate">
              <option value="">{{'all' | labelName}}</option>
              <option value="true">{{'yes' | labelName}}</option>
              <option value="false">{{'no' | labelName}}</option>
            </select>
          </div>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'search_promo_code'">
        <div class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <label for="search_promo_code">
            {{ 'PromoCode' | labelName}}
          </label>
          <input type="text" class="form-control" id="search_promo_code" [(ngModel)]="playerParams.search_promo_code"
            [placeholder]="'search' | labelName" />
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="'status'">
        <div class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <label for="status">{{'status' | labelName}}</label>
          <select class="form-control" id="status" [(ngModel)]="playerParams.status">
            <option value="">{{'all' | labelName}}</option>
            <option value="true">{{'active' | labelName}}</option>
            <option value="false">{{'inactive' | labelName}}</option>
          </select>
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'date_range'">

        <!-- <div class="col-md p-0"> -->
        <div class="form-group" [ngClass]="{ 'select-form ':  isDeepMatchValues(initialFilters[item], playerParams[item])   }">
          <label for="status">{{'DATE_RANGE' | labelName}}</label>
          <input autocomplete="off" class="form-control"[attr.data-placeholder]="'search' | labelName"
            [placeholder]="'search_time_period' | labelName" type="text" id="time_period" [format]="format" daterangetime_picker
            (onSelect)="setTimePeriod($event)" />
        </div>
        <!-- </div> -->
      </ng-container>

      <ng-container *ngSwitchCase="'search_by_id'">
        <div class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <label for="search_by_id">
            {{'by_id' | labelName}}
          </label>
          <input type="text" class="form-control" id="search_by_id" [(ngModel)]="playerParams.search_by_id"
            [placeholder]="'search' | labelName" />
        </div>

      </ng-container>

      <ng-container *ngSwitchCase="'search_by_nic'">
        <div class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <div>
            <label for="search_by_nic">
              <!-- National ID -->
               {{'national_id' | labelName}}
            </label>
            <input type="text" class="form-control" id="search_by_nic" [(ngModel)]="playerParams.search_by_nic"
              [placeholder]="'search' | labelName" />
          </div>
        </div>
      </ng-container>

      <!-- User Filter -->
      <ng-container *ngSwitchCase="'search'">
        <div *ngIf="this.searchPlaceholder" class="form-group" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <label for="search2"  [title]="searchPlaceholder" >
            {{'search' | labelName}}
             <i
             data-toggle="tooltip"
              [title]="searchPlaceholder"
              class="fas fa-question-circle"
              [ngClass]="{ 'text-danger': playerParams.search.length > 0 }"></i>
          </label>
          <input type="text" class="form-control" id="search2" [(ngModel)]="playerParams.search"
            [placeholder]="searchPlaceholder" />
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="'currency'">
        <div class="form-group" *ngIf="availableCurrencies.length > 1" [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <label for="currency">{{'currency' | labelName}}</label>
          <select class="form-control" id="currency" (change)="onCurrencyChange($event)" [(ngModel)]="selectedCurrencies">
            <option value="" selected disabled>{{'select' | labelName}}</option>
            <option value="ALL">{{'all' | labelName}}</option>
            <option *ngFor="let currency of availableCurrencies" [value]="currency.id">{{ currency.code }}</option>
          </select>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="'creation_type'">
        <div class="form-group" [ngClass]="{ 'select-form ': initialFilters[item] != playerParams[item] }">
          <div>
            <label for="creation_type">{{'registration_type' | labelName}}</label>
            <select class="form-control" id="creation_type" [(ngModel)]="playerParams.creation_type">
              <option value="">{{'all' | labelName}}</option>
              <option value="0">{{'normal_signup' | labelName}}</option>
              <option value="1">{{'one_click_signup' | labelName}}</option>
            </select>
          </div>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="'player_type'">
        <div class="form-group " [ngClass]="{ 'select-form ':  initialFilters[item]  != playerParams[item]  }">
          <label for="sb_player_type">{{'player_type' | labelName}}</label>
          <select class="form-control" id="sb_player_type" [(ngModel)]="playerParams.player_type" (change)="onPlayerTypeChange($event.target)">
            <option value="" selected disabled>{{'select' | labelName}}</option>
            <option value="all_players">{{'all_players' | labelName}}</option>
            <option value="real_players">{{'real_players' | labelName}}</option>
            <option value="bot_players">{{'bot_players' | labelName}}</option>
          </select>
        </div>

      </ng-container>
      <!-- Agent -->
      <ng-container *ngSwitchCase="'adminId'">
        <div class="form-group" [ngClass]="{ 'select-form ': isDeepMatchValues(initialFilters[item], playerParams[item]) }">
          <label for="agent_id">{{'agent' | labelName}}</label>
          <select class="form-control" select2 id="agent_id" title="Select"
            (onSelect)="filterSelectAgent($event)" [(ngModel)]="playerParams.adminId" [attr.data-placeholder]="'select' | labelName">
            <!-- <option value="">{{'select_agent' | labelName}}</option> -->
            <option *ngFor="let adminUser of userList" [value]="adminUser.id"> {{adminUser.first_name}}
            </option>
          </select>
        </div>
      </ng-container>

      <ng-container *ngSwitchCase="'affiliated_data'">
        <!-- <ng-container *ngIf="affiliateEnable" > -->
          <div class="col-md p-0">
            <div class="form-group" [ngClass]="{ 'select-form ': initialFilters[item]  != playerParams[item] }">
              <label for="affiliated_data">{{'affiliated_players' | labelName}}</label>
              <select class="form-control" id="affiliated_data" (change)="selectAffiliateAction($event)"
                [(ngModel)]="playerParams.affiliated_data" [attr.data-placeholder]="'search' | labelName">
                <option value="" disabled selected hidden>Select</option>
                <option value="all">{{'all_affiliated_players' | labelName}}</option>
                <option value="code">{{'search_by_affiliate_code' | labelName}}</option>
              </select>
            </div>
          </div>
        <!-- </ng-container> -->

      </ng-container>

            <ng-container *ngSwitchCase="'affiliated_code'">

        <!-- <ng-container *ngIf="searchByCode && affiliateEnable"> -->
          <div class="col-md p-0">
            <div class="form-group" [ngClass]="{ 'select-form ': initialFilters.affiliated_data  != playerParams.affiliated_data}">
              <label for="affiliated_code">{{'affiliate_code' | labelName}}</label>
              <select class="form-control" [placeholder]="'search' | labelName" search_select2 [checkAffiliate]="true" [WithData]="WithData"
                [url]="searchCodeUrl" id="affiliated_code" (onSelect)="selectAffiliateCode($event)"></select>
            </div>
          </div>
        <!-- </ng-container> -->
      </ng-container>

      <ng-container *ngSwitchCase="'additional_filter'">
        <div>
          <div class="form-group moreBtnGroup" *ngIf="filterGroups && filterGroups.length > 1">
            <button type="button" class="btn moreFilter" (click)="toggleFilterModal()">
              <p>{{'more_filters' | labelName}} <span *ngIf="filterCount != 0" class="filterCount"> {{filterCount}} </span></p>
              <img src="../../../../assets/dist/svg/filterIcon.svg" alt="filter">
            </button>
          </div>
        </div>
      </ng-container>
      <!-- default -->
      <ng-container *ngSwitchDefault>
      </ng-container>
    </switch>

  </ng-container>

</ng-template>

<!-- filtersModal -->
<filter-modal [show]="showFilterModal"  (close)="toggleFilterModal()" (apply)="applyFilters()"
  [disableApplyButton]="isLoader"
  (reset)="playerResetFilter()"
  title="Players">
  <ng-container *ngFor="let filter of filterGroups">
    <ng-container *ngIf="customFilters && customFilters[filter] && customFilters[filter].length > 0 && filter !== 'MAIN_FILTERS' && filter !== 'BUTTON'">
      <div class="innerFilterBodyWrap">
        <h6>{{filter | labelName }}</h6>
        <div class="innerFilterWrap">
          <ng-container *ngTemplateOutlet="reusableFilterBlock; context: { filterKey: filter, mainFilter: false }"></ng-container>
        </div>
      </div>
    </ng-container>
  </ng-container>
</filter-modal>

<ng-template #buttonList let-buttonType="buttonType">


  <ng-container
    *ngIf="this.permissionService.checkPlayerKeyExistsAndHasAllPermissions() && this.permissionService.PERMISSIONS?.players?.C && !isSuperAdmin">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button '">
      <a [routerLink]="['/players', 0]" class="btn btn-success mb-2 mb-sm-0 btn-sm custom-primary-btn  buttonNotShow"
      >
      <i class="fas fa-user-plus"></i>{{'create' | labelName}}</a>
    </div>

  </ng-container>
    <ng-container
    *ngIf="(isBulkDepositEnable || isBulkWithdrawEnable || isBulkEditEnable) && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule)) ||isSuperAdmin && !this.permissionService.isManager ">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
      <a [routerLink]="['/players/jobs']" class="btn btn-info mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow"
      >
      <i class="fas fa-list"></i>{{'job_list' | labelName}}</a>
    </div>

  </ng-container>
  <ng-container

    *ngIf="(!size && isBulkEditEnable && this.permissionService.checkPermission('players','U')) && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule))">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
      <a class="btn btn-info mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow" type="button" (click)="openEditModal()" >
      <i class="fas fa-edit"></i>{{'bulk_edit' | labelName}}</a>
    </div>

  </ng-container>
  <ng-container
    *ngIf="(size && (this.permissionService.checkBotDomain() == true)) && ((!isAgent || !this.permissionService.isManager))">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
      <a class="btn btn-info mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow" type="button" (click)="openBotModal()"
      >
      <i class="fas fa-edit"></i>{{'mark_as_bot' | labelName}}</a>
    </div>

  </ng-container>
  <ng-container
    *ngIf="(size && (!this.permissionService.isSuperAdmin) &&  this.permissionService.PERMISSIONS?.referral_settings?.U)">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
      <a class="btn btn-info mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow " type="button"
       (click)="openDisableReferralModal()">
      <i class="fas fa-lock"></i>{{'disable_referral' | labelName}}</a>
    </div>

  </ng-container>

  <ng-container *ngIf="!size && (isBulkDepositEnable || isBulkWithdrawEnable) && (this.permissionService.checkPermission('fund_transfer_deposit','C') || this.permissionService.checkPermission('fund_transfer_withdraw','C')) && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule))">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
      <button

      class="btn btn-primary mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow" type="button" (click)="openCsvModal()"
      >
      <i class="fas fa-upload"></i>{{'bulk_csv_upload' | labelName}}</button>
    </div>
  </ng-container>


  <ng-container *ngIf="(!isSuperAdmin && size && this.permissionService.checkPermission('enable_withdrawal_requests','T')) && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule))">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
      <a class="btn btn-info mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow " type="button"
       (click)="openWithdrawRequestModal()">
      <i class="fa fa-ban"></i>{{'restrict_withdrawal_request' | labelName}}</a>
    </div>
  </ng-container>
      <ng-container *ngIf="size && this.permissionService.checkPermission('bulk_assign_bonus','C')">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button '">
      <button

      class="btn btn-warning mb-2 mb-sm-0 btn-sm custom-primary-btn  buttonNotShow" type="button" (click)="openBonusModal()">
      <i class="fas fa-edit"></i>{{'bulk_assign_bonus' | labelName}}<span class="badge badge-light">{{this.size}}</span>
      <span class="sr-only">{{this.size}} Users</span>
    </button>
    </div>
  </ng-container>
  <ng-container *ngIf="size && isBulkEditEnable && this.permissionService.PERMISSIONS?.players?.U && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule))">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button '">
      <button class="btn btn-warning mb-2 mb-sm-0 btn-sm custom-primary-btn  buttonNotShow" type="button" (click)="openEditModal()"
      >
      <i class="fas fa-edit"></i>{{'selected_bulk_edit' | labelName}}<span class="badge badge-light">{{this.size}}</span>
      <span class="sr-only">{{this.size}} Users</span>
    </button>
    </div>
  </ng-container>
  <ng-container  *ngIf="size && isBulkWithdrawEnable && this.permissionService.checkPermission('fund_transfer_withdraw','C')  && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule))">
  <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
    <button
    class="btn btn-danger mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow " type="button" (click)="openModal(2)"
   >
    <i class="fas fa-minus-circle"></i>{{'bulk_withdraw' | labelName}}<span class="badge badge-light">{{this.size}}</span>
    <span class="sr-only">{{this.size}} Users</span>
  </button>
  </div>
</ng-container>



  <ng-container  *ngIf="size && isBulkDepositEnable && this.permissionService.checkPermission('fund_transfer_deposit','C') && ((!isAgent && DomainForDisableModule.includes(isAllowedModule)) || !DomainForDisableModule.includes(isAllowedModule))">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button'">
      <button

      class="btn btn-primary mb-2 mb-sm-0 btn-sm custom-primary-btn buttonNotShow" type="button" (click)="openModal(1)"
      >
      <i class="fas fa-user-plus"></i>{{'bulk_deposit' | labelName}}<span class="badge badge-light">{{this.size}}</span>
      <span class="sr-only">{{this.size}} Users</span>
    </button>
    </div>
  </ng-container>





</ng-template>
