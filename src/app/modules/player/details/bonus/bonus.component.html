<section class="content"  *ngIf="activeTab == 'player_bonus'">
    <div class="container-fluid px-0">

        <div class="row">

            <div class="col-md-12">
                <div class="card mb-0">
                  <div class="card-header py-0 mt-0">
                    <div class="row w-100 d-flex align-items-center mainHeadFilter mx-0">
                      <div class="form-group col-12 col-md-6 pl-0 mb-0">
                          <h2 class="card-title custom-h2-heading">{{'bonus_filter' | labelName}}</h2>
                      </div>

                    </div>
                  </div>
                    <!-- /.card-header -->
                    <div class="card-body ">
                      <div class="row mx-0 filterRow">
                        <div class="col-xxl-9">
                          <div class="row">
                            <div class="col-md">
                              <div class="form-group" [ngClass]="{ 'select-form': initialFilters.kind != bonusParams.kind }">
                                <label for="kind">{{'bonus_kind' | labelName}}</label>
                                <select class="form-control" id="kind" data-placeholder="Select Kind"
                                    [(ngModel)]="bonusParams.kind">
                                    <option value="">{{'select' | labelName}}</option>
                                    <option *ngFor="let kind of kinds" [value]="kind.value"> {{
                                        kind.name }} </option>
                                </select>
                            </div>
                            </div>
                            <div class="col-md">
                              <div class="form-group" [ngClass]="{ 'select-form': initialFilters.promotion_title != bonusParams.promotion_title }">
                                <label for="promotion_title">{{'promotion_title' | labelName}}</label>
                                <input type="text" class="form-control fnt-14" id="promotion_title" [(ngModel)]="bonusParams.promotion_title"
                                  [placeholder]="'search' | labelName" />
                              </div>
                            </div>
                            <div class="col-md">
                              <div class="form-group" [ngClass]="{ 'select-form': initialFilters.status != bonusParams.status }">
                                <label for="status">{{'status' | labelName}}</label>
                                <select class="form-control" id="status" [(ngModel)]="bonusParams.status">
                                <option value="">All</option>
                                  <option value="active">{{'active' | labelName}}</option>
                                  <option value="cancelled">{{'cancelled' | labelName}}</option>
                                  <option value="claimed">{{'claimed' | labelName}}</option>
                                  <option value="expired">{{'expired' | labelName}}</option>
                                </select>
                              </div>
                            </div>
                            <div class="col-md">
                              <div *ngIf="!lifetime_records">
                                <div class="form-group" [ngClass]="{ 'select-form': filterService.isDeepMatchValues(initialFilters.time_period, bonusParams.time_period) }">
                                    <label for="time_period_bonus">{{'DATE_RANGE' | labelName}}</label>
                                    <input autocomplete="off" class="form-control" data-placeholder="'search' | labelName"
                                        [placeholder]="'search_time_period' | labelName" type="text" id="time_period_bonus"
                                        [format]="format" daterangetime_picker (onSelect)="setTimePeriod($event)" />
                                </div>
                            </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-xxl-3 resetSearch">
                          <div class="form-group custom-btn-secondary form-group-head m-0 text-start">
                            <button [disabled]="isLoader" type="button" class="btn custom-secondry-btn" (click)="bonusResetFilter()"><i
                              class="fa fa-refresh"></i> {{'reset_filter' | labelName}}
                          </button>
                            <button [disabled]="isLoader" type="button" (click)="filterBonus()" class="btn custom-secondry-btn searchBtn fnt-12"><i class="fa fa-search"></i>
                              {{'search' | labelName}} </button>
                          </div>
                        </div>

                        <div class="col-12 pl-0">
                  <div class="row rowBtnWrap justify-content-end mx-0">
                    <!-- buttons -->
                    <div class="d-inline-flex align-items-center flex-grow-1 flex-wrap headFiltersRow buttonList" id="buttonList">
                      <ng-container *ngTemplateOutlet="buttonActions"></ng-container>
                    </div>

                    <!-- Exports -->
                    <ng-container>
                      <div class="d-flex justify-content-end align-items-center selectExport pr-0 ">
                        <ng-container *ngTemplateOutlet="buttonExport"></ng-container>
                      </div>
                    </ng-container>

                  </div>
                </div>
                        <!-- <div class="col-12 px-0">
                          <div class="row rowBtnWrap justify-content-end mx-0">
                            <div class="d-flex justify-content-end align-items-center selectExport">
                              <ng-container *ngIf="this.permissionService.checkPermission('lifetime_records','T')">
                                <div class="icheck-primary d-inline">
                                  <input type="checkbox" [checked]="lifetime_records" (change)="changeRecords()" id="checkboxPrimary2">
                                  <label for="checkboxPrimary2">{{'lifetime_records' | labelName}}</label>
                                </div>
                            </ng-container>
                            </div>
                          </div>
                        </div> -->
                      </div>
                      <div class="row">
                        <div class="col-12">
                          <app-other-languages [language]="selectedLanguage"
                            (languageChange)="filterSelectLanguage($event)" class="d-flex justify-content-end"></app-other-languages>
                        </div>
                      </div>
                        <div class="row mx-0">
                        <div class="table-responsive custom-table hCustom">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>
                                            <div>
                                              {{'id' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'kind' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'promotion_title' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'bonus_amount' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'initial_rollover_balance' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'remaining_rollover_balance' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'achieved_rollover_target' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'status' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'activated_at' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'transaction_id' | labelName}}
                                            </div>
                                        </th>
                                        <th>
                                            <div>
                                              {{'action' | labelName}}
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        *ngFor="let user of bonuses | paginate: { itemsPerPage: bonusParams.size, currentPage: bonusP, totalItems: total , id:'bonusid' }">
                                        <td>{{user.id }}</td>
                                        <td>{{getKindName(user.kind) }}</td>
                                        <td><a [routerLink]="['/bonus/deposit-details', user.bonus_id,getRedirectionString(user.kind)]">{{languageText(user.promotion_title) }}</a></td>
                                        <td>{{((user.kind == 'losing' || user.kind == 'losing_both')  && user?.bonus_claim_type == 'automatic' ) ? (user.bonus_amount_losing_sum || 0) :
                                            ((user.kind == 'deposit_instant') ? (user.bonus_amount_deposit_sum || 0) : user.bonus_amount)}}</td>
                                        <td>{{user.initial_rollover_balance !== null ?
                                            user.initial_rollover_balance
                                            : 'NA'}}</td>
                                        <td>{{user.rollover_balance || 'NA'}}</td>
                                        <td>{{ user.initial_rollover_balance !== null ?
                                            (user.initial_rollover_balance -
                                            user.rollover_balance) : 'NA' }}</td>
                                        <td>{{user.status }}</td>
                                        <td>{{user.activated_at | date: 'medium' }}</td>
                                        <td>{{ user.transaction_id === -99999 ? '-' : user.transaction_id }}</td>
                                        <td>
                                            <ng-container
                                                *ngIf="(user.kind=='losing' && user?.bonus_claim_type == 'automatic') || (user?.kind=='deposit_instant'); else noAction">
                                                <button
                                                    *ngIf="user.kind=='losing' && user?.bonus_claim_type == 'automatic'"
                                                    class="btn edit-btn-grp"
                                                    (click)="getUserLosingClaimHistory(user)"><i
                                                        class="fa fa-eye" aria-hidden="true"></i></button>
                                                <button *ngIf="user?.kind=='deposit_instant'"
                                                    class="btn edit-btn-grp"
                                                    (click)="getUserInstantHistory(user)"><i class="fa fa-eye"
                                                        aria-hidden="true"></i></button>
                                            </ng-container>

                                            <ng-template #noAction>{{'na' | labelName}}</ng-template>
                                        </td>
                                    </tr>

                                    <tr *ngIf="bonuses && bonuses.length === 0">
                                        <td colspan="11" class="tnodata"> {{'no_records_found' | labelName}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="custom-pagination mt-3" *ngIf="total>0">
                            <div class="pagination-item-list">
                                <pagination-controls id="bonusid" (pageChange)="bonusPageChanged($event)"
                                    (pageChange)="bonusP = $event" [nextLabel]=this.permissionService.PAGINATION.NEXT [previousLabel]=this.permissionService.PAGINATION.PREVIOUS [maxSize] = this.permissionService.PAGINATION.MAX_SIZE></pagination-controls>

                                    <select class="form-control" id="size" (change)="filterBonus()" [(ngModel)]="bonusParams.size">
                                      <option *ngFor="let pageSize of pageSizes" [value]="pageSize.name">{{pageSize.name}}</option>
                                    </select>
                            </div>

                            <div class="showPage">
                              <span class="show-entries">
                                <span appPaginationDisplay  class="ngx-pagination"
                                      [currentPage]="bonusP"
                                      [itemsPerPage]="bonusParams.size"
                                      [totalItems]="total">
                                </span>
                              </span>
                            </div>
                        </div>




                    </div>

                </div>
                <!-- /.card -->

            </div>



        </div>
      </div>

    </div><!-- /.container-fluid -->
</section>

<ng-template #loader>

    <div class="w-full text-center custom-loader-div d-flex justify-content-center ">
        <!-- <img class="chart-loader" src="./assets/dist/gif/loader.gif" /> -->
        <div class="custom-loader"></div>
    </div>

</ng-template>


<div class="modal fade bd-example-modal-xl custom-modal" id="modal-view" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">{{'claim_history' | labelName}}</h4>

          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>


        <div class="modal-body">
          <div class="table-responsive custom-table hCustom">
            <table *ngIf="!isClaimHistoryLoader; else loader" class="table table-bordered">
              <thead>
                <tr>
                  <th>{{'id' | labelName}} </th>
                  <th>{{'bonus_amount' | labelName}}</th>
                  <th>{{'claimed_at' | labelName}}</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="let user of claimHistoryData | paginate: { itemsPerPage: claimHistoryParams.size, currentPage: claimHistoryP, totalItems: claimHistoryTotal, id: 'claimid' }">
                  <td>{{user.id }}</td>
                  <td>{{user.bonus_amount}}</td>
                  <td>{{user.claimed_at | date: 'dd-MM-yyyy HH:mm:ss' }}</td>
                </tr>

                <tr *ngIf="claimHistoryData && claimHistoryData.length === 0">
                  <td colspan="9" class="tnodata">{{'no_data_found' | labelName}}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="custom-pagination">
            <div class="pagination-item-list">
          <pagination-controls id="claimid" (pageChange)="onClaimPageChanged($event)"
            (pageChange)="claimHistoryP = $event"></pagination-controls>
          </div>
        </div>
        </div>

      </div>
    </div>
  </div>

  <div class="modal fade bd-example-modal-xl custom-modal" id="modal-view-instant" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">{{'instant_history' | labelName}}</h4>

          <button type="button" class="close edit-btn-grp" data-dismiss="modal" aria-label="Close">
            <i class="fa fa-times" aria-hidden="true"></i>
          </button>
        </div>


        <div class="modal-body">
          <div class="table-responsive custom-table hCustom">
            <table *ngIf="!isInstantHistoryLoader; else loader" class="table table-bordered">
              <thead>
                <tr>
                  <th>
                    <div>
                      {{'id' | labelName}}
                    </div>
                  </th>
                  <th>
                    <div>
                      {{'deposit_amount' | labelName}}
                    </div>
                  </th>
                  <th>
                    <div>
                      {{'bonus_amount' | labelName}}
                    </div>
                  </th>
                  <th>
                    <div>
                      {{'claimed_at' | labelName}}
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  *ngFor="let user of instantHistoryData | paginate: { itemsPerPage: instantHistoryParams.size, currentPage: instantHistoryP, totalItems: instantHistoryTotal, id: 'instantid' }">
                  <td>{{user.id }}</td>
                  <td>{{user.deposit_amount}}</td>
                  <td>{{user.bonus_amount}}</td>
                  <td>{{user.created_at | date: 'dd-MM-yyyy HH:mm:ss' }}</td>
                </tr>

                <tr *ngIf="instantHistoryData && instantHistoryData.length === 0">
                  <td colspan="9" class="tnodata">{{'no_data_found' | labelName}}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="custom-pagination mt-3" >
            <div class="pagination-item-list">
          <pagination-controls id="instantid" (pageChange)="onInstantPageChanged($event)"
            (pageChange)="instantHistoryP = $event"></pagination-controls>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>


  <div class="modal fade custom-modal" id="modal-bulk-bonus" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-md">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">{{'assign_bonus' | labelName}}</h4>
        </div>

        <form [formGroup]="bonusForm" (ngSubmit)="submitBonusForm()">
          <div class="modal-body">
            <span class="modal-note"><strong class="mr-1">{{'note' | labelName}}:</strong>{{'only_eligible_bonuses_will_appear_based_on' | labelName}}</span>
            <ul class="modal-note">
              <li>{{'matching_currency_and_vip_level' | labelName}}</li>
              <li>Referral type and promo code (if set) match the user's</li>
              <li>{{'category_match_if_player_category_is_enabled' | labelName}}</li>
              <li>{{'user_hasnt_used_or_queued_the_bonus_before' | labelName}}</li>
            </ul>

            <span class="modal-note"><strong class="mr-1">Note for deposit-type bonuses:</strong>  The bonus will still activate, but the rollover target will not be applied in the following cases:</span>
            <ul class="modal-note">
              <li><strong class="mr-1">{{'burning_bonus' | labelName}}:</strong>{{'active_bonus_still_has_unutilized_amount' | labelName}}</li>
              <li><strong class="mr-1">{{'week_day_mismatch' | labelName}}:</strong>{{'bonus_is_for_another_day' | labelName}}</li>
              <li><strong class="mr-1">{{'deposit_type' | labelName}}:</strong> The user’s selected deposit method (e.g., manual, payment gateway) does not match the type required by the bonus</li>
              <li>
               <strong class="mr-1">{{'special_note' | labelName}}:</strong> Deposits used to claim an instant bonus are included, as no rollover is set on them
              </li>
            </ul>

              

            <div class="form-group mb-0">
              <label class="position-static " for="bonus_type">{{'bonus_type' | labelName}}<span
                class="text-danger">*</span></label><br>
                <div class="form-check form-check-inline">
                  <input formControlName="bonus_type" class="form-check-input" type="radio" id="playerBonusTypeDeposit" [value]="1">
                  <label class="form-check-label bg-transparent" for="playerBonusTypeDeposit">{{'deposit' | labelName}}</label>
                </div>
                <div class="form-check form-check-inline">
                  <input formControlName="bonus_type" class="form-check-input" type="radio" id="playerBonusTypeCashback" [value]="2">
                  <label class="form-check-label bg-transparent" for="playerBonusTypeCashback">{{'cashback' | labelName}}</label>
                </div>
              <div class="invalid-feedback" *ngIf="submitted && bonusFormControls.bonus_type.errors">
                <div *ngIf="bonusFormControls.bonus_type.errors.required">{{'bonus_type_is_required' | labelName}}</div>
              </div>
            </div>
            <div class="form-group" *ngIf="bonusFormControls.bonus_type.value">
              <label for="bonus_id">{{'bonus_list' | labelName}}<span
                class="text-danger">*</span></label>
              <select class="form-control" select2 (onSelect)="selectBonus($event)" id="bonus_id" data-placeholder="Select a Bonus" style="width: 100%;">
                <option [value]="null">{{'select_bonus' | labelName}}</option>
                <ng-container *ngIf="bonusFormControls.bonus_type.value == 1">
                  <option *ngFor="let bonus of depositBonuses" [value]="bonus.id"> {{ languageText(bonus.promotion_title) }} ({{bonus.code}}) </option>
                </ng-container>
                <ng-container *ngIf="bonusFormControls.bonus_type.value == 2">
                  <option *ngFor="let bonus of cashbackBonuses" [value]="bonus.id"> {{ languageText(bonus.promotion_title) }} ({{bonus.code}}) </option>
                </ng-container>
              </select>
              <div class="invalid-feedback" *ngIf="submitted && bonusFormControls.bonus_id.errors">
                  <div *ngIf="bonusFormControls.bonus_id.errors.required">{{'bonus_is_required' | labelName}}</div>
              </div>
          </div>
          <div class="form-group" *ngIf="bonusFormControls.bonus_type.value == 1">
            <div *ngIf="!isLoader; else loader">
              <ng-container>
                  <label for="deposit_id">{{'deposit_list' | labelName}}
                    <i
                      data-toggle="tooltip"
                      title="This deposit list is based on the user's deposits (including admin cash deposits) within the selected bonus campaign period. Only deposits not used for any previous bonus rollover target are shown."
                      class="fas fa-question-circle"
                    ></i>
                  </label>
                  <select class="form-control" select2 (onSelect)="selectDeposit($event)"  id="deposit_id" formControlName="deposit_id"[attr.data-placeholder]="'select_a_deposit' | labelName" style="width: 100%;">
                    <option [value]="null"> {{  depositLists.length > 0  ? "Select a Deposit" : "No Deposits Found!" }}</option>
                    <ng-container>
                      <option *ngFor="let deposit of depositLists" [value]="deposit.id">
                        Amount:{{deposit.amount}} |
                        Date:{{ (deposit.createdAt | date: 'MMM d, y HH:mm:ss') }} |
                        Type:{{ deposit.paymentProviderId != undefined ? getPaymentGatewayName(deposit.paymentProviderId): "Manual" }}
                      </option>
                    </ng-container>
                  </select>
              </ng-container>
             </div>
            <!-- <p style="font-size: 12px;" *ngIf="depositLists.length < 1 && bonusSelected">{{'no_deposits_found' | labelName}}</p> -->
            <div class="invalid-feedback" *ngIf="submitted && bonusFormControls.deposit_id.errors">
              <div *ngIf="bonusFormControls.deposit_id.errors.required">{{'deposit_is_required' | labelName}}</div>
            </div>
          </div>
          </div>
          <div class="modal-footer justify-content-between">
            <button type="submit" class="custom-primary-btn" [disabled]="submitted">{{'save_changes' | labelName}}</button>
            <button type="button" class="btn custom-primary-btn" data-dismiss="modal" (click)="closeBonusModal()">{{'close' | labelName}}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <ng-template #loader>
    <div class="w-full text-center custom-loader-div d-flex justify-content-center">
      <div class="custom-loader"></div>
    </div>
  </ng-template>


  <ng-template #buttonExport>


                              <ng-container *ngIf="this.permissionService.checkPermission('lifetime_records','T')">
                                <div class="icheck-primary d-inline">
                                  <input type="checkbox" [checked]="lifetime_records" (change)="changeRecords()" id="checkboxPrimary2">
                                  <label for="checkboxPrimary2">{{'lifetime_records' | labelName}}</label>
                                </div>
                            </ng-container>
</ng-template>


<ng-template #buttonActions>
  <ng-container *ngTemplateOutlet="buttonList; context: { buttonType: true }"></ng-container>
  <div class="buttonId showBtn">

    <a class="btn btn-warning mb-2 mb-sm-0 btn-sm custom-primary-btn showMore" type="button" data-toggle="dropdown"
      href="#">
      {{'show_more' | labelName}}
    </a>
    <div class="dropdown-menu dropdown-menu-right" id="showMore">
      <div class="dropdown-item">
        <ng-container *ngTemplateOutlet="buttonList; context: { buttonType: false }"></ng-container>

      </div>
    </div>
  </div>

</ng-template>



<ng-template #buttonList let-buttonType="buttonType">


  <ng-container
     *ngIf="!this.permissionService.isSuperAdmin && assignBonusActive">
    <div [ngClass]="buttonType == true ? 'buttonId' : 'dropdown-menu-button '">
      <a (click)="openBonusModal()" class="btn btn-success mb-2  btn-sm custom-primary-btn  buttonNotShow"
      >
      <i class="fa fa-edit custom-icon"></i>{{'assign_bonus' | labelName}}</a>
    </div>

  </ng-container>






</ng-template>
