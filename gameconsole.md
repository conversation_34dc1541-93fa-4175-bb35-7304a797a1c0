# Game Seeding and Provider-Aggregator Control System

## 📌 Executive Summary

This document outlines the enhanced game seeding mechanism that allows the Super Admin to dynamically assign providers to aggregators, switch them as needed, and manage provider visibility at the tenant level. The system ensures that game data, consoles, and categories reflect the correct aggregator-provider relationship with proper syncing and no data duplication.

## 🎯 Project Objectives

### Primary Goals
To provide the Super Admin with full control over game seeding by:
- **Dynamic Selection**: Allowing the selection of a source aggregator for any provider during the seeding process
- **Provider Switching**: Supporting the switching of a provider from one aggregator to another—even if the provider is already active in the system
- **Data Consistency**: Ensuring all data consistency, visibility, and de-duplication checks are handled at the time of seeding

### Core Requirements
- **Multi-Provider Support**: Enable Super Admin to seed games from any provider under any aggregator
- **Seamless Switching**: Allow switching a provider from one aggregator to another at the tenant level
- **Data Integrity**: Ensure that:
  - Only one provider-aggregator pair is visible per tenant
  - System hides previous aggregator's data if provider is switched
  - No duplicate data appears in Casino Table, Game Console, or Provider List

## ⚙️ Technical Implementation

### 1. Provider Creation and Switching System

#### Core Capabilities
- **Provider Switching**: Super Admin can switch a provider from one aggregator to another via the Game Seed Logs page
- **Dynamic Selection**: During seeding, Super Admin can select a specific provider under a chosen aggregator
- **Provider Dropdown**: For Aggregator Provider Dropdown is visible with all available providers

#### System Behavior on Save
- **Data Hiding**: System hides the old aggregator's data (games, tables, consoles) at the tenant level
- **Data Activation**: The new aggregator's data becomes active
- **Duplication Prevention**: No duplicate entries remain—only the new provider-aggregator pair is visible

### 2. Game Console Creation and Syncing

#### Super Admin Privileges
- **Full Access**: Has access to all providers and game data
- **Console Creation**: Can create game consoles across all providers and aggregators

#### Category Management
- **Universal Creation**: Super Admin creates categories from any provider, regardless of aggregator
- **Filtering Support**: Aggregator info is used by Super Admin to filter data during setup

#### Syncing Logic
- **Targeted Sync**: On sync, games are only synced to the tenants who have explicit access to that provider-aggregator combination
- **Example Implementation**:
  - 8Dex uses Ezugi via Whitcliff → only games from Ezugi (Whitcliff) are synced to 8Dex
  - Other tenants using Ezugi (ST8) see only those games
- **Isolation Guarantee**: Ensures provider-aggregator isolation and correct game visibility per tenant

## ⚠️ Known Issues and Solutions

### Issue 1: Popular Game Management
**Problem**: When there is any game specified as popular game from aggregator 1, now game is changed to other aggregator  
**Solution**: Old provider data will be hidden. But new Provider games need to add

### Issue 2: Blocked Game Management
**Problem**: If Tenant admin blocks any of the game, category or page of aggregator  
**Solution**: No solution confirmed yet. For now need to re-configure

### Issue 3: Website Content/SEO Management
**Problem**: Data can be not visible because provider is not same  
**Solution**: No solution confirmed yet. For now need to re-configure

### Issue 4: Bonus System Integration
**Problem**: Issue with bonus system we define specific game for specific provider, Cashback Bonus  
**Solution**: No solution confirmed yet. For now need to re-configure

### Issue 5: Reports Filtering
**Problem**: Issue with all reports while filtering  
**Solution**: Old provider and new Provider data will be shown cumulatively. Tenant Level both provider data is shown in single data

### Issue 6: Dashboard Calculations
**Problem**: All dashboard calculation provider wise gets affected  
**Solution**: Old provider and new Provider data will be shown cumulatively. Tenant Level both provider data is shown in single data

## 📋 Implementation Task Breakdown

### Phase 1: Core Infrastructure & Database Schema

#### Task 1.1: Provider-Aggregator Management System
**Assignee:** Prashanth Ganji  
**Priority:** High  
**Estimated Time:** 3-4 days

**Requirements:**
- Create provider-aggregator identification table
- Implement provider switching capability
- Ensure single provider-aggregator pair visibility per tenant
- Add aggregator dropdown for provider selection

**Database Schema:**
```sql
-- Not planned yet
CREATE TABLE provider_aggregator_mapping (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    provider_id INTEGER NOT NULL,
    aggregator_id INTEGER NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    switched_from_aggregator_id INTEGER NULL,
    switched_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_tenant_provider (tenant_id, provider_id, status)
);
```

#### Task 1.2: Game Seeding Logs Enhancement
**Assignee:** Prashanth Ganji  
**Priority:** High  
**Estimated Time:** 2-3 days

**Requirements:**
- Enhance Game Seed Logs page for provider switching
- Add provider-aggregator selection interface
- Implement data hiding mechanism for old aggregator
- Create audit trail for all switches

### Phase 2: Game Console & Category Management

#### Task 2.1: Game Console Creation System
**Assignee:** Development Team  
**Priority:** Medium  
**Estimated Time:** 4-5 days

**Requirements:**
- Super Admin access to all providers and game data
- Category creation from any provider regardless of aggregator
- Aggregator-based filtering for Super Admin setup
- Proper syncing behavior per tenant-provider-aggregator combination

#### Task 2.2: Syncing Logic Implementation
**Assignee:** Prashanth Ganji  
**Priority:** High  
**Estimated Time:** 3-4 days

**Requirements:**
- Sync games only to tenants with explicit provider-aggregator access
- Ensure provider-aggregator isolation
- Implement correct game visibility per tenant
- Handle data consistency during sync operations

### Phase 3: Issue Resolution & Reconfiguration

#### Task 3.1: Popular Games Migration
**Assignee:** TBD  
**Priority:** High  
**Estimated Time:** 2-3 days

**Requirements:**
- Implement popular games migration logic
- Create manual reconfiguration interface
- Add validation for popular games in new provider
- Document migration requirements

#### Task 3.2: Blocked Games Management
**Assignee:** TBD  
**Priority:** High  
**Estimated Time:** 2-3 days

**Requirements:**
- Implement blocked games migration logic
- Create manual reconfiguration interface
- Add validation for blocked items in new provider
- Document reconfiguration requirements

#### Task 3.3: Website Content/SEO Management
**Assignee:** Development Team  
**Priority:** Medium  
**Estimated Time:** 3-4 days

**Requirements:**
- Create content migration strategy
- Implement SEO data preservation
- Add manual reconfiguration tools
- Document content management requirements

#### Task 3.4: Bonus System Reconfiguration
**Assignee:** TBD  
**Priority:** High  
**Estimated Time:** 4-5 days

**Requirements:**
- Implement bonus migration logic
- Create game mapping for bonus transfers
- Add manual bonus reconfiguration interface
- Validate bonus compatibility with new provider

### Phase 4: Reporting & Dashboard Integration

#### Task 4.1: Reports Cumulative Data Display
**Assignee:** TBD  
**Priority:** High  
**Estimated Time:** 3-4 days

**Requirements:**
- Display old and new provider data cumulatively
- Implement tenant-level combined data view
- Ensure historical data preservation
- Add provider transition tracking in reports

#### Task 4.2: Dashboard Calculations Update
**Assignee:** TBD  
**Priority:** High  
**Estimated Time:** 2-3 days

**Requirements:**
- Update dashboard to show cumulative data
- Implement combined provider calculations
- Ensure data consistency across dashboards
- Add provider transition indicators

### Phase 5: Testing & Documentation

#### Task 5.1: Comprehensive Testing
**Assignee:** QA Team  
**Priority:** High  
**Estimated Time:** 5-6 days

**Requirements:**
- Test provider switching scenarios
- Validate data consistency
- Test all reconfiguration processes
- Performance testing with large datasets

#### Task 5.2: Documentation Creation
**Assignee:** Prashanth Ganji  
**Priority:** Medium  
**Estimated Time:** 2-3 days

**Requirements:**
- Create detailed game seeding process documentation
- Document provider switching procedures
- Create troubleshooting guide
- User manual for Super Admin operations

## 📝 Action Items from Meeting

### Immediate Actions
- [ ] **Prashanth Ganji:** Create document detailing game seeding process
- [ ] **Prashanth Ganji:** Create provider/aggregator identification table
- [ ] **Prashanth Ganji:** Ensure inactive game status highlighting in database
- [ ] **Prashanth Ganji:** Implement popular games reconfiguration after provider switch

### Key Questions to Address
1. How will the team ensure new system doesn't affect existing functionalities?
2. What happens to games no longer provided by selected aggregator?
3. How to handle financial data visibility for certain tenants?

## ✅ Success Criteria

### Functional Requirements
- [ ] Super Admin can switch providers between aggregators seamlessly
- [ ] Only one provider-aggregator pair visible per tenant
- [ ] All reconfiguration processes work correctly
- [ ] Reports show cumulative data properly
- [ ] No data duplication in any system component

### Non-Functional Requirements
- [ ] Zero-downtime provider switching
- [ ] Sub-second response times for game loading
- [ ] Complete audit trail for all operations
- [ ] 99.9% system availability during operations

## 🔗 Dependencies
- Database schema changes approval
- Provider API documentation and access
- Tenant configuration data backup
- QA environment setup for testing
- Stakeholder approval for reconfiguration processes

---

**Document Version:** 1.0  
**Last Updated:** August 5, 2025  
**Meeting Type:** Internal Discussion (Final)  
**Document Reference:** Game Seeding and Provider-Aggregator Control
