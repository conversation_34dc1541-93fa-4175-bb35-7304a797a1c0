# JIRA Child Ticket: Queue Service Provider Switch Implementation

## Ticket Information
- **Parent Ticket**: SGS-2705 - Game Console Tasks, Enhancement – Aggregator Based Provider Seeding Option
- **Child Ticket Title**: Update Queue Service with Provider Switch Functionality
- **Type**: Task
- **Priority**: High
- **Assignee**: <PERSON><PERSON>anth Ganji
- **Estimated Time**: 5-6 days

## 📋 Task Description

Update the queue service game seeding mechanism to support dynamic provider-aggregator  functionality for all providers listed in `CRON_GAME_SEED_PROVIDERS`. The implementation must include both game seeding and category synchronization with provider switch capabilities.

## 🎯 Objective

Implement provider switch functionality for all game seed providers in the queue service:

### Target Providers (from CRON_GAME_SEED_PROVIDERS)
```javascript
const PROVIDERS_TO_UPDATE = [
  'darwin',
  'evolution',
  'ezugi',
  'funky',
  'lottery777',
  'netent',
  'pgsoft',
  'red tiger',
  'spinocchio',
  'st8',
  'whitecliff',
  'spribe',
  'turbogames'
];
```

### Core Requirements
1. **Provider Switch Functionality**: Enable switching any provider between different aggregators
2. **Game Seeding**: Update game population for all 13 providers with aggregator context
3. **Category Synchronization**: Implement category sync functionality for each provider
4. **Data Consistency**: Ensure proper data hiding/showing when providers are switched
5. **Aggregator Identification**: Add aggregator context to all game population and category sync functions

## 📝 Current Implementation Analysis

### Current Queue Service Implementation Analysis

#### Existing Switch Statement Structure
```javascript
switch (parseInt(providerId)) {
  case funkyCasinoProviderId:
    data.providerTitle = 'Funky'
    await funkyGamesGamePopulation({ tenantIds })
    break
  case darwinProviderId:
    data.providerTitle = 'Darwin'
    await darwinGamePopulation({ tenantIds })
    break
  case pgSoftProviderId:
    data.providerTitle = 'PGsoft'
    await pgSoftGamePolulation({ tenantIds })
    break
  case spinocchioProviderId:
    data.providerTitle = 'Spinocchio'
    await spinocchioGamePopulation({ tenantIds })
    break
  case lottery777ProviderId:
    data.providerTitle = 'Lottery777'
    await lotteryGamePopulation({ tenantIds })
    break
  case whitecliffProviderId:
    data.providerTitle = 'Whitecliff'
    await whiteCliffGamePopulation({ tenantIds })
    break
  case st8ProviderId:
    console.log(tenantIds,'sdasdadsasdadsadsasdasd')
    await st8GamePopulation({ tenantIds })
    break
  case spribeProviderId:
    await spribeGamePopulation({ tenantIds })
    break
  case evolutionProviderId:
    data.providerTitle = 'Evolution'
    await ezugiGamesPopulation({ tenantIds, providerName: 'evolution' })
    break
  case parseInt(ezugiProviderId):
    data.providerTitle = 'Ezugi'
    await ezugiGamesPopulation({ tenantIds, providerName: 'ezugi' })
    break
  case parseInt(redTigerProviderId):
    data.providerTitle = 'Red Tiger'
    await ezugiGamesPopulation({ tenantIds, providerName: 'redtiger' })
    break
  case parseInt(netEntProviderId):
    data.providerTitle = 'NetEnt'
    await ezugiGamesPopulation({ tenantIds, providerName: 'netent' })
    break
  case turboGamesProviderId:
    data.providerTitle = 'TurboGames'
    await turboGamesPopulation({ tenantIds })
    break
  default:
    break
}
```

#### Missing Providers from CRON_GAME_SEED_PROVIDERS
The current switch statement is missing some providers that need to be added:
- **turbogames** (partially implemented as turboGamesProviderId)
- Any other providers from the CRON_GAME_SEED_PROVIDERS list not currently handled


### 2. New Helper Functions Required

#### Provider-Aggregator Mapping Functions
```javascript
/**
 * Get the current aggregator for a provider-tenant combination
 */
async function getProviderAggregator(providerId, tenantIds) {
  // Query provider_aggregator_mapping table
  // Return current active aggregator for the provider-tenant pair
}

/**
 * Get switch context information
 */
async function getSwitchContext(providerId, tenantIds) {
  return {
    isSwitch: await isProviderSwitch(providerId, tenantIds),
    previousAggregator: await getPreviousAggregator(providerId, tenantIds),
    switchTimestamp: await getSwitchTimestamp(providerId, tenantIds)
  }
}

/**
 * Handle data visibility during provider switch
 */
async function handleProviderSwitch(providerId, tenantIds, newAggregatorId) {
  // Hide old aggregator data
  await hideOldAggregatorData(providerId, tenantIds)
  // Activate new aggregator data
  await activateNewAggregatorData(providerId, tenantIds, newAggregatorId)
  // Log switch activity
  await logProviderSwitch(providerId, tenantIds, newAggregatorId)
}
```

### 3. Enhanced Game Population Functions

#### Update All Game Population Functions
Each game population function needs to be updated to handle:
- **Aggregator Context**: Accept aggregator information
- **Switch Handling**: Manage data visibility during switches
- **Data Consistency**: Ensure no duplicate data

```javascript
// Example: Enhanced funkyGamesGamePopulation with provider switch support
async function funkyGamesGamePopulation({
  tenantIds,
  aggregatorId,
  switchContext
}) {
  try {
    // Handle provider switch if applicable
    if (switchContext.isSwitch) {
      await handleProviderSwitch('funky', tenantIds, aggregatorId)
    }

    // Existing game population logic
    // ... current implementation for funky games

    // Add aggregator context to seeded games
    await updateGameAggregatorMapping(tenantIds, 'funky', aggregatorId)

    // Log seeding activity
    await logGameSeeding('funky', tenantIds, aggregatorId, switchContext)

  } catch (error) {
    await logSeedingError('funky', tenantIds, aggregatorId, error)
    throw error
  }
}

// Example: New category sync function for funky provider
async function funkyCategoriesSync({
  tenantIds,
  aggregatorId,
  switchContext
}) {
  try {
    // Handle category visibility during provider switch
    if (switchContext.isSwitch) {
      await handleCategorySwitch('funky', tenantIds, aggregatorId)
    }

    // Sync categories from funky provider
    const categories = await fetchFunkyCategories(aggregatorId)
    await syncCategoriesToTenants(categories, tenantIds, 'funky', aggregatorId)

    // Update category-aggregator mapping
    await updateCategoryAggregatorMapping(tenantIds, 'funky', aggregatorId)

    // Log category sync activity
    await logCategorySync('funky', tenantIds, aggregatorId, categories.length)

  } catch (error) {
    await logCategorySyncError('funky', tenantIds, aggregatorId, error)
    throw error
  }
}
```

### 4. Database Schema Updates

#### Provider-Aggregator Mapping Table
```sql
CREATE TABLE IF NOT EXISTS provider_aggregator_mapping (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    provider_id INTEGER NOT NULL,
    aggregator_id INTEGER NOT NULL,
    provider_name VARCHAR(255) NOT NULL,
    aggregator_name VARCHAR(255) NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    switched_from_aggregator_id INTEGER NULL,
    switched_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_tenant_provider_active (tenant_id, provider_id, status)
);
```

#### Game Seeding Audit Table
```sql
CREATE TABLE IF NOT EXISTS game_seeding_audit (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    provider_id INTEGER NOT NULL,
    provider_name VARCHAR(255) NOT NULL,
    aggregator_id INTEGER NOT NULL,
    aggregator_name VARCHAR(255) NOT NULL,
    action_type ENUM('seed', 'switch', 'hide', 'activate') NOT NULL,
    games_count INTEGER DEFAULT 0,
    switch_context JSON NULL,
    status ENUM('success', 'failed', 'partial') DEFAULT 'success',
    error_message TEXT NULL,
    execution_time_ms INTEGER NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📋 Implementation Tasks

### Phase 1: Database and Core Infrastructure (2 days)
- [ ] Create provider_aggregator_mapping table
- [ ] Create game_seeding_audit table
- [ ] Implement helper functions for provider-aggregator mapping
- [ ] Create switch context management functions

### Phase 2: Queue Service Enhancement (2 days)
- [ ] Update main switch statement with aggregator context
- [ ] Implement provider switch handling logic
- [ ] Add data visibility management functions
- [ ] Update all game population function signatures

### Phase 3: Game Population and Category Sync Functions Update (3 days)

#### Game Population Functions (All 13 Providers)
- [ ] Update **darwinGamePopulation** with aggregator support + create **darwinCategoriesSync**
- [ ] Update **evolutionGamesPopulation** with aggregator support + create **evolutionCategoriesSync**
- [ ] Update **ezugiGamesPopulation** with aggregator support + create **ezugiCategoriesSync**
- [ ] Update **funkyGamesGamePopulation** with aggregator support + create **funkyCategoriesSync**
- [ ] Update **lotteryGamePopulation** with aggregator support + create **lottery777CategoriesSync**
- [ ] Update **ezugiGamesPopulation** (netent) with aggregator support + create **netentCategoriesSync**
- [ ] Update **pgSoftGamePolulation** with aggregator support + create **pgSoftCategoriesSync**
- [ ] Update **ezugiGamesPopulation** (redtiger) with aggregator support + create **redTigerCategoriesSync**
- [ ] Update **spinocchioGamePopulation** with aggregator support + create **spinocchioCategoriesSync**
- [ ] Update **st8GamePopulation** with aggregator support + create **st8CategoriesSync**
- [ ] Update **whiteCliffGamePopulation** with aggregator support + create **whitecliffCategoriesSync**
- [ ] Update **spribeGamePopulation** with aggregator support + create **spribeCategoriesSync**
- [ ] Update **turboGamesPopulation** with aggregator support + create **turboGamesCategoriesSync**

#### Category Sync Implementation Requirements
Each category sync function must:
- [ ] Fetch categories from provider API with aggregator context
- [ ] Handle category visibility during provider switches
- [ ] Sync categories to specified tenant IDs
- [ ] Update category-aggregator mapping in database
- [ ] Log all category sync activities for audit trail

### Phase 4: Testing and Validation (1 day)
- [ ] Test provider switching scenarios
- [ ] Validate data consistency during switches
- [ ] Test game population with different aggregators
- [ ] Performance testing with large datasets
- [ ] Error handling and rollback testing

## 🔍 Acceptance Criteria

### Functional Requirements
- [ ] Queue service supports dynamic provider-aggregator assignment
- [ ] All game population functions accept aggregator context
- [ ] Provider switching hides old aggregator data and shows new data
- [ ] No duplicate games appear across different aggregators
- [ ] Complete audit trail for all seeding and switching activities

### Technical Requirements
- [ ] Database schema supports provider-aggregator mapping
- [ ] All existing functionality remains intact
- [ ] Error handling and logging implemented
- [ ] Performance impact is minimal
- [ ] Code follows existing patterns and standards

### Data Integrity Requirements
- [ ] Only one provider-aggregator pair visible per tenant
- [ ] Historical data preserved during switches
- [ ] Rollback capability in case of failures
- [ ] Consistent game visibility across all components

## 🚨 Risk Considerations

### High Risk Items
1. **Data Loss**: Ensure no game data is lost during provider switches
2. **Downtime**: Minimize service interruption during updates
3. **Performance**: Large tenant datasets may impact seeding performance
4. **Rollback**: Complex rollback scenarios if switches fail

### Mitigation Strategies
1. **Backup Strategy**: Full data backup before any switch operations
2. **Gradual Rollout**: Test with small tenant sets first
3. **Monitoring**: Real-time monitoring of seeding operations
4. **Rollback Plan**: Automated rollback procedures for failed switches

## 📚 Dependencies

### Internal Dependencies
- Provider-aggregator mapping database schema
- Game seeding audit system
- Tenant configuration management
- Queue service infrastructure

### External Dependencies
- Provider API availability
- Database performance optimization
- Monitoring and alerting systems
- Backup and recovery procedures

## 🎯 Success Metrics

### Performance Metrics
- Game seeding time: < 30 seconds per provider
- Provider switch time: < 60 seconds
- Zero data loss during switches
- 99.9% success rate for seeding operations

### Quality Metrics
- Complete audit trail for all operations
- No duplicate games across aggregators
- Consistent data visibility
- Error rate < 0.1%

---

**Created**: August 5, 2025  
**Parent Ticket**: SGS-2705  
**Estimated Effort**: 5-6 days  
**Priority**: High
