# Game Seed & Provider Switch Implementation Task

## Project Overview
Implementation of a comprehensive game seeding system that enables super admins to manage game providers effectively at both super admin and tenant levels, with provider switching capabilities and proper data synchronization.

## Meeting Summary
**Date:** Tuesday, August 5, 2025  
**Type:** Internal Discussion (Final)  
**Participants:** <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

## Key Requirements

### Core Functionality
1. **Game Seeding System** - Enable super admins to manage game providers comprehensively
2. **Provider Management** - Switch between game providers at tenant level
3. **Aggregator Identification** - Clear categorization system to avoid duplication
4. **Data Synchronization** - Maintain data integrity during provider switches
5. **Reporting Structure** - Manage data by titles to prevent duplication

### Access Control
- **Super Admin Access** - Full access to all providers and games
- **Tenant Level Access** - Limited to active provider only
- **Provider Filtering** - Same provider name cannot appear across multiple tenants simultaneously

## Technical Implementation Plan

### Phase 1: Database Schema & Core Infrastructure

#### 1.1 Provider & Aggregator Management
```sql
-- Provider/Aggregator identification table
CREATE TABLE game_providers_aggregators (
    id SERIAL PRIMARY KEY,
    provider_name VARCHAR(255) NOT NULL,
    aggregator_name VARCHAR(255) NOT NULL,
    provider_type ENUM('direct', 'aggregated') DEFAULT 'direct',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_provider_aggregator (provider_name, aggregator_name)
);
```

#### 1.2 Game Seeding Table
```sql
-- Game seeding management
CREATE TABLE game_seeds (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    provider_id INTEGER NOT NULL,
    aggregator_id INTEGER NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    game_title VARCHAR(255) NOT NULL,
    game_status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
    is_popular BOOLEAN DEFAULT FALSE,
    is_blocked BOOLEAN DEFAULT FALSE,
    seeded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_sync_at TIMESTAMP NULL,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (provider_id) REFERENCES game_providers_aggregators(id),
    INDEX idx_tenant_provider (tenant_id, provider_id),
    INDEX idx_game_status (game_status),
    INDEX idx_popular_blocked (is_popular, is_blocked)
);
```

#### 1.3 Provider Switch Tracking
```sql
-- Provider switch audit log
CREATE TABLE provider_switch_log (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL,
    old_provider_id INTEGER,
    new_provider_id INTEGER NOT NULL,
    switch_reason TEXT,
    switched_by INTEGER NOT NULL,
    switched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    affected_games_count INTEGER DEFAULT 0,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (switched_by) REFERENCES admins(id)
);
```

### Phase 2: Backend API Implementation

#### 2.1 Provider Management APIs
- `GET /api/super-admin/providers` - List all providers and aggregators
- `POST /api/super-admin/providers` - Add new provider/aggregator
- `PUT /api/super-admin/providers/{id}` - Update provider information
- `DELETE /api/super-admin/providers/{id}` - Deactivate provider

#### 2.2 Game Seeding APIs
- `GET /api/super-admin/game-seeds` - List all game seeds
- `POST /api/super-admin/game-seeds/sync` - Sync games from provider
- `PUT /api/super-admin/game-seeds/{id}` - Update game seed status
- `POST /api/tenant/{id}/provider-switch` - Switch tenant provider

#### 2.3 Tenant Game Management APIs
- `GET /api/tenant/games` - Get tenant-specific games
- `PUT /api/tenant/games/{id}/popular` - Mark/unmark as popular
- `PUT /api/tenant/games/{id}/block` - Block/unblock game
- `GET /api/tenant/games/popular` - Get popular games
- `GET /api/tenant/games/blocked` - Get blocked games

### Phase 3: Frontend Implementation

#### 3.1 Super Admin Interface
- **Provider Management Dashboard**
  - List all providers and aggregators
  - Add/edit provider information
  - Provider status management
  - Aggregator assignment interface

- **Game Seeding Dashboard**
  - Game synchronization interface
  - Bulk game management
  - Provider-wise game listing
  - Seeding status monitoring

#### 3.2 Tenant Interface
- **Game Selection Dropdown**
  - Aggregator name identification
  - Provider-specific game filtering
  - Game availability status

- **Game Configuration**
  - Popular games management
  - Blocked games management
  - Game visibility controls

### Phase 4: Provider Switch Implementation

#### 4.1 Switch Process
1. **Pre-Switch Validation**
   - Check active games count
   - Validate new provider availability
   - Backup current configuration

2. **Switch Execution**
   - Update tenant provider mapping
   - Sync games from new provider
   - Update game visibility
   - Log switch activity

3. **Post-Switch Configuration**
   - Reconfigure popular games
   - Update blocked games list
   - Validate menu names
   - Check bonus system compatibility

#### 4.2 Data Migration
- **Game Mapping** - Map games between providers where possible
- **Configuration Transfer** - Transfer applicable settings
- **Report Continuity** - Maintain reporting data integrity
- **Audit Trail** - Complete switch tracking

## Action Items & Assignments

### Prashanth Ganji
- [ ] Create detailed game seeding process documentation
- [ ] Design provider/aggregator identification table
- [ ] Implement database schema for game seeds
- [ ] Ensure inactive game status highlighting
- [ ] Develop popular games reconfiguration logic
- [ ] Create reporting structure to prevent duplication

### Chaitanya Prajapati
- [ ] Assign bonus management tasks to Shivan
- [ ] Assign blocker games tasks to Shivan
- [ ] Create phased task breakdown for team
- [ ] Review and approve technical specifications
- [ ] Coordinate provider switch testing

### Ashish Vishvakarma
- [ ] Create comprehensive findings report
- [ ] Document provider transition scenarios (NetAgent India → Whiteclips → Estates)
- [ ] Validate reporting data handling
- [ ] Test provider name vs ID reporting logic

### Rahul Rajawat
- [ ] Validate single provider per tenant constraint
- [ ] Review technical architecture
- [ ] Ensure system performance optimization
- [ ] Coordinate with infrastructure team

## Risk Mitigation

### Technical Risks
- **Data Loss During Switch** - Implement comprehensive backup and rollback
- **Game Duplication** - Strict validation and deduplication logic
- **Performance Impact** - Optimize queries and implement caching
- **Provider API Failures** - Implement retry mechanisms and fallbacks

### Business Risks
- **Service Disruption** - Implement zero-downtime switching
- **Configuration Loss** - Backup and restore mechanisms
- **Report Inconsistency** - Maintain historical data integrity
- **User Experience** - Smooth transition with minimal impact

## Testing Strategy

### Unit Testing
- Provider management functions
- Game seeding logic
- Switch process validation
- Data integrity checks

### Integration Testing
- Provider API integration
- Database transaction testing
- Frontend-backend integration
- Cross-tenant isolation

### Performance Testing
- Large dataset handling
- Concurrent switch operations
- Database query optimization
- API response times

## Success Criteria

### Functional Requirements
- [ ] Super admin can manage all providers and games
- [ ] Tenants can switch providers seamlessly
- [ ] Game visibility respects provider constraints
- [ ] Popular/blocked games reconfigure correctly
- [ ] Reports maintain data integrity

### Non-Functional Requirements
- [ ] Zero-downtime provider switching
- [ ] Sub-second game loading times
- [ ] 99.9% system availability
- [ ] Complete audit trail maintenance
- [ ] Scalable to 1000+ tenants

## Timeline & Milestones

### Week 1-2: Foundation
- Database schema implementation
- Core API development
- Basic provider management

### Week 3-4: Game Seeding
- Game synchronization logic
- Seeding dashboard
- Data validation

### Week 5-6: Provider Switch
- Switch process implementation
- Configuration migration
- Testing and validation

### Week 7-8: Integration & Testing
- End-to-end testing
- Performance optimization
- Documentation completion

## Documentation Requirements

- [ ] Technical specification document
- [ ] API documentation
- [ ] User manual for super admins
- [ ] Tenant configuration guide
- [ ] Troubleshooting guide
- [ ] Database schema documentation
